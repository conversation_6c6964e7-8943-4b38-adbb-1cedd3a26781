import multer from 'multer';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import mriUploadModel from '../models/mriUploadModel.js';
import analysisModel from '../models/analysisModel.js';

// Configure multer for file upload
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = 'uploads/mri';
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        // Handle .nii.gz files properly
        let extension = path.extname(file.originalname);
        if (file.originalname.toLowerCase().endsWith('.nii.gz')) {
            extension = '.nii.gz';
        }
        const uniqueName = `${uuidv4()}-${Date.now()}${extension}`;
        cb(null, uniqueName);
    }
});

const fileFilter = (req, file, cb) => {
    // Accept common medical image formats
    const allowedTypes = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/tiff', 'image/tif',
        'application/dicom', 'application/octet-stream', 'application/gzip', 'application/x-gzip'
    ];

    const fileName = file.originalname.toLowerCase();
    const isDicom = fileName.endsWith('.dcm');
    const isNifti = fileName.endsWith('.nii') || fileName.endsWith('.nii.gz');

    if (allowedTypes.includes(file.mimetype) || isDicom || isNifti) {
        cb(null, true);
    } else {
        cb(new Error('Invalid file type. Please upload medical image files (JPEG, PNG, TIFF, DICOM, NIfTI)'), false);
    }
};

export const upload = multer({
    storage: storage,
    limits: {
        fileSize: 50 * 1024 * 1024 // 50MB limit
    },
    fileFilter: fileFilter
});

// Upload MRI image
export const uploadMRI = async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: "No file uploaded"
            });
        }

        const mriUpload = new mriUploadModel({
            userId: req.user._id,
            fileName: req.file.filename,
            originalName: req.file.originalname,
            filePath: req.file.path,
            fileSize: req.file.size,
            mimeType: req.file.mimetype,
            status: 'uploaded'
        });

        await mriUpload.save();

        res.json({
            success: true,
            message: "MRI image uploaded successfully",
            uploadId: mriUpload._id,
            fileName: req.file.originalname
        });

    } catch (error) {
        console.log(error);
        res.status(500).json({
            success: false,
            message: "Error uploading file"
        });
    }
};

// Get user's uploads
export const getUserUploads = async (req, res) => {
    try {
        const uploads = await mriUploadModel.find({ userId: req.user._id })
            .populate('analysisId')
            .populate('reportId')
            .sort({ uploadDate: -1 });

        res.json({
            success: true,
            uploads
        });

    } catch (error) {
        console.log(error);
        res.status(500).json({
            success: false,
            message: "Error fetching uploads"
        });
    }
};

// Delete upload
export const deleteUpload = async (req, res) => {
    try {
        const { uploadId } = req.params;
        
        const upload = await mriUploadModel.findOne({
            _id: uploadId,
            userId: req.user._id
        });

        if (!upload) {
            return res.status(404).json({
                success: false,
                message: "Upload not found"
            });
        }

        // Delete file from filesystem
        if (fs.existsSync(upload.filePath)) {
            fs.unlinkSync(upload.filePath);
        }

        // Delete related analysis and report if they exist
        if (upload.analysisId) {
            await analysisModel.findByIdAndDelete(upload.analysisId);
        }

        await mriUploadModel.findByIdAndDelete(uploadId);

        res.json({
            success: true,
            message: "Upload deleted successfully"
        });

    } catch (error) {
        console.log(error);
        res.status(500).json({
            success: false,
            message: "Error deleting upload"
        });
    }
};
