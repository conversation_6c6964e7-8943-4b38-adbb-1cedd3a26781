/* eslint-disable no-unused-vars */
// services/flaskApiService.js
import axios from 'axios';

const FLASK_API_BASE_URL = 'http://127.0.0.1:8000';

class FlaskApiService {
    constructor() {
        this.baseURL = FLASK_API_BASE_URL;
        this.timeout = 300000; // 5 minutes
    }

    /**
     * Analyze brain tumor using file upload
     * @param {File} file - The NIfTI file to analyze
     * @param {string} patientId - Patient identifier
     * @returns {Promise} Analysis results
     */
    async analyzeWithFile(file, patientId) {
        try {
            const formData = new FormData();
            formData.append('nifti_file', file);
            formData.append('patient_id', patientId);

            const response = await axios.post(
                `${this.baseURL}/analyze`,
                formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    },
                    timeout: this.timeout
                }
            );

            return response.data;
        } catch (error) {
            throw this.handleError(error);
        }
    }

    /**
     * Analyze brain tumor using file path (if Flask API has access to the file system)
     * @param {string} imagePath - Path to the FLAIR image file (ending with _0000)
     * @param {string} patientId - Patient identifier
     * @returns {Promise} Analysis results
     */
    async analyzeWithPath(imagePath, patientId) {
        try {
            const requestData = {
                patient_id: patientId,
                image: imagePath
            };

            const response = await axios.post(
                `${this.baseURL}/analyze`,
                requestData,
                {
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    timeout: this.timeout
                }
            );

            return response.data;
        } catch (error) {
            throw this.handleError(error);
        }
    }

    /**
     * Check if Flask API is available
     * @returns {Promise<boolean>} True if API is available
     */
    async checkHealth() {
        try {
            const response = await axios.post(`${FLASK_API_BASE_URL}/`, {
                timeout: 5000
            });
            return response.status === 200;
        } catch (error) {
            return false;
        }
    }

    /**
     * Convert blob to File object
     * @param {Blob} blob - The blob data
     * @param {string} filename - Original filename
     * @returns {File} File object
     */
    blobToFile(blob, filename) {
        return new File([blob], filename, {
            type: 'application/octet-stream',
            lastModified: Date.now()
        });
    }

    /**
     * Fetch file from your backend and convert to File object
     * @param {string} backendUrl - Your backend URL
     * @param {string} fileId - File ID
     * @param {string} filename - Original filename
     * @returns {Promise<File>} File object
     */
    async fetchFileFromBackend(backendUrl, fileId, filename) {
        try {
            const response = await axios.get(`${backendUrl}/api/files/${fileId}`, {
                responseType: 'blob'
            });

            return this.blobToFile(response.data, filename);
        } catch (error) {
            throw new Error(`Failed to fetch file: ${error.message}`);
        }
    }

    /**
     * Handle API errors
     * @param {Error} error - Axios error
     * @returns {Error} Formatted error
     */
    handleError(error) {
        if (error.response) {
            // Server responded with error status
            const message = error.response.data?.error || error.response.data?.message || 'Analysis failed';
            return new Error(message);
        } else if (error.request) {
            // Request made but no response received
            return new Error('No response from analysis server. Please check if the Flask API is running.');
        } else {
            // Something else happened
            return new Error(error.message || 'Unknown error occurred');
        }
    }

    /**
     * Format analysis results for display
     * @param {Object} results - Raw results from Flask API
     * @returns {Object} Formatted results
     */
    formatResults(results) {
        if (!results) return null;

        return {
            tumorCore: results.tumor_core,
            wholeTumor: results.whole_tumor,
            enhancingTumor: results.enhancing_tumor,
            volumes: results.volumes_mm3 ? {
                tumorCore: results.volumes_mm3.tumor_core,
                wholeTumor: results.volumes_mm3.whole_tumor,
                enhancingTumor: results.volumes_mm3.enhancing_tumor,
                brainVolume: results.volumes_mm3.brain_volume
            } : null,
            confidence: results.confidence ? {
                tumorCore: results.confidence.tumor_core,
                wholeTumor: results.confidence.whole_tumor,
                enhancingTumor: results.confidence.enhancing_tumor
            } : null,
            stage: results.stage ? results.stage.brain_volume : null,
            raw: results // Keep raw results for debugging
        };
    }

    /**
     * Get tumor severity level based on percentages
     * @param {Object} results - Analysis results
     * @returns {string} Severity level
     */
    getTumorSeverity(results) {
        if (!results) return 'Unknown';

        const tumorCorePercent = parseFloat(results.tumor_core?.replace('%', '') || 0);
        const wholeTumorPercent = parseFloat(results.whole_tumor?.replace('%', '') || 0);

        if (wholeTumorPercent === 0) return 'No Tumor Detected';
        if (wholeTumorPercent < 1) return 'Low';
        if (wholeTumorPercent < 3) return 'Moderate';
        if (wholeTumorPercent < 5) return 'High';
        return 'Critical';
    }

    /**
     * Generate recommendations based on analysis results
     * @param {Object} results - Analysis results
     * @returns {string} Recommendations
     */
    generateRecommendations(results) {
        if (!results) return 'Unable to generate recommendations without analysis results.';

        const severity = this.getTumorSeverity(results);
        const stage = results.stage?.brain_volume;

        let recommendations = '';

        switch (severity) {
            case 'No Tumor Detected':
                recommendations = 'No significant tumor detected. Continue regular monitoring as recommended by your physician.';
                break;
            case 'Low':
                recommendations = 'Low tumor volume detected. Regular monitoring and follow-up with oncologist recommended.';
                break;
            case 'Moderate':
                recommendations = 'Moderate tumor presence. Immediate consultation with neuro-oncologist advised for treatment planning.';
                break;
            case 'High':
                recommendations = 'Significant tumor volume detected. Urgent consultation with multidisciplinary tumor board recommended.';
                break;
            case 'Critical':
                recommendations = 'Critical tumor volume detected. Immediate medical attention and aggressive treatment planning required.';
                break;
            default:
                recommendations = 'Consult with your healthcare provider for proper interpretation of results.';
        }

        if (stage) {
            recommendations += ` Brain volume classification: ${stage}.`;
        }

        return recommendations;
    }
}

// Create singleton instance
const flaskApiService = new FlaskApiService();

export default flaskApiService;