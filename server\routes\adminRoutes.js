import express from 'express';
import {
    getDashboardStats,
    getAllUsers,
    updateDoctorStatus,
    deleteUser,
    getAllReports,
    getAnalytics,
    fixDoctorVerification,
    getAllFlaskAnalyses
} from '../controllers/adminController.js';
import { adminAuth } from '../middleware/roleAuth.js';

export const adminRouter = express.Router();

// Get dashboard statistics
adminRouter.get('/dashboard', adminAuth, getDashboardStats);

// Get all users
adminRouter.get('/users', adminAuth, getAllUsers);

// Approve/reject doctor
adminRouter.put('/doctor/:doctorId/status', adminAuth, updateDoctorStatus);

// Delete user
adminRouter.delete('/user/:userId', adminAuth, deleteUser);

// Get all reports
adminRouter.get('/reports', adminAuth, getAllReports);

// Get all Flask API analyses
adminRouter.get('/flask-analyses', adminAuth, getAllFlaskAnalyses);

// Get analytics
adminRouter.get('/analytics', adminAuth, getAnalytics);

// Fix doctor verification (temporary endpoint)
adminRouter.post('/fix-doctor-verification', adminAuth, fixDoctorVerification);
