import { useState, useEffect, useContext, useCallback } from 'react';
import { AppContent } from '../context/AppContext';
import axios from 'axios';
import { toast } from 'react-toastify';
import Navbar from '../components/Navbar';
import ImageUpload from '../components/ImageUpload';
import AnalysisResults from '../components/AnalysisResults';
import ReportCard from '../components/ReportCard';
import FlaskAnalysisCard from '../components/FlaskAnalysisCard';
import tokenManager from '../utils/tokenManager';

const UserDashboard = () => {
    const { backendUrl, userData, isLoggedin, loading: contextLoading } = useContext(AppContent);
    const [uploads, setUploads] = useState([]);
    const [reports, setReports] = useState([]);
    const [flaskAnalyses, setFlaskAnalyses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [activeTab, setActiveTab] = useState('upload');

    const fetchUserData = useCallback(async () => {
        try {
            setLoading(true);

            // Get authentication token using tokenManager
            const token = tokenManager.getToken() || localStorage.getItem('token');
            console.log('🔐 UserDashboard authentication check:');
            console.log('  - tokenManager token:', !!tokenManager.getToken());
            console.log('  - legacy token:', !!localStorage.getItem('token'));
            console.log('  - final token:', !!token);
            console.log('  - userData from context:', userData);

            if (!token) {
                console.log('❌ No token found, showing login error');
                toast.error('Please log in to access your dashboard');
                return;
            }

            const authHeaders = {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            };

            // Fetch uploads
            const uploadsResponse = await axios.get(`${backendUrl}/api/upload/user`, {
                headers: authHeaders
            });
            if (uploadsResponse.data.success) {
                setUploads(uploadsResponse.data.uploads);
            }

            // Fetch reports
            const reportsResponse = await axios.get(`${backendUrl}/api/report/user/all`, {
                headers: authHeaders
            });
            if (reportsResponse.data.success) {
                setReports(reportsResponse.data.reports);
            }

            // Fetch Flask API analysis history
            console.log('🔄 Fetching Flask API analysis history...');
            const flaskResponse = await axios.get(`${backendUrl}/api/fastapi/history`, {
                headers: authHeaders
            });
            console.log('📥 Flask API history response:', flaskResponse.data);
            if (flaskResponse.data.success) {
                console.log('✅ Flask analyses found:', flaskResponse.data.analyses.length);
                setFlaskAnalyses(flaskResponse.data.analyses);
            } else {
                console.log('❌ Failed to fetch Flask analyses:', flaskResponse.data.message);
            }

        } catch (error) {
            toast.error('Error fetching data');
            console.error(error);
        } finally {
            setLoading(false);
        }
    }, [backendUrl, userData]);

    useEffect(() => {
        // Wait for context to load before fetching data
        if (!contextLoading && isLoggedin) {
            fetchUserData();
        } else if (!contextLoading && !isLoggedin) {
            console.log('❌ User not logged in according to context');
            setLoading(false);
        }
    }, [contextLoading, isLoggedin, fetchUserData]);

    const handleUploadSuccess = () => {
        fetchUserData();
        setActiveTab('history');
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50">
                <Navbar />
                <div className="flex justify-center items-center h-96">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            <Navbar />
            
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* Account Verification Notice */}
                {userData && !userData.isAccountVerified && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                        <div className="flex">
                            <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-yellow-800">
                                    Account Verification Pending
                                </h3>
                                <div className="mt-2 text-sm text-yellow-700">
                                    <p>
                                        Your account is not yet verified. You can still use all features, but we recommend verifying your email for enhanced security.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Welcome Section */}
                <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">
                        Welcome back, {userData?.name}!
                    </h1>
                    <p className="text-gray-600">
                        Upload your MRI scans for AI-powered brain tumor analysis
                    </p>
                </div>

                

                {/* Tab Navigation */}
                <div className="bg-white rounded-lg shadow-sm mb-8">
                    <div className="border-b border-gray-200">
                        <nav className="-mb-px flex space-x-8 px-6">
                            <button
                                onClick={() => setActiveTab('upload')}
                                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                    activeTab === 'upload'
                                        ? 'border-blue-500 text-blue-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                            >
                                Upload MRI
                            </button>
                            <button
                                onClick={() => setActiveTab('history')}
                                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                    activeTab === 'history'
                                        ? 'border-blue-500 text-blue-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                            >
                                Upload History
                            </button>
                            <button
                                onClick={() => setActiveTab('reports')}
                                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                    activeTab === 'reports'
                                        ? 'border-blue-500 text-blue-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                            >
                                My Reports
                            </button>
                        </nav>
                    </div>

                    <div className="p-6">
                        {activeTab === 'upload' && (
                            <ImageUpload onUploadSuccess={handleUploadSuccess} />
                        )}

                        {activeTab === 'history' && (
                            <div className="space-y-4">
                                <h3 className="text-lg font-medium text-gray-900 mb-4">Upload History</h3>
                                {uploads.length === 0 ? (
                                    <p className="text-gray-500 text-center py-8">No uploads yet</p>
                                ) : (
                                    uploads.map((upload) => (
                                        <AnalysisResults 
                                            key={upload._id} 
                                            upload={upload} 
                                            onUpdate={fetchUserData}
                                        />
                                    ))
                                )}
                            </div>
                        )}

                        {activeTab === 'reports' && (
                            <div className="space-y-6">
                                <h3 className="text-lg font-medium text-gray-900 mb-4">My Reports</h3>

                                {reports.length === 0 && flaskAnalyses.length === 0 ? (
                                    <p className="text-gray-500 text-center py-8">No reports generated yet</p>
                                ) : (
                                    <div className="space-y-8">
                                        {/* Flask API Analysis Results */}
                                        {flaskAnalyses.length > 0 && (
                                            <div>
                                                <h4 className="text-md font-medium text-gray-800 mb-4 flex items-center">
                                                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                                                    Flask API Analysis Results ({flaskAnalyses.length})
                                                </h4>
                                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                                    {flaskAnalyses.map((analysis) => (
                                                        <FlaskAnalysisCard
                                                            key={analysis._id}
                                                            analysis={analysis}
                                                            onUpdate={fetchUserData}
                                                        />
                                                    ))}
                                                </div>
                                            </div>
                                        )}

                                        {/* Traditional Reports */}
                                        {reports.length > 0 && (
                                            <div>
                                                <h4 className="text-md font-medium text-gray-800 mb-4 flex items-center">
                                                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                                                    Traditional Reports ({reports.length})
                                                </h4>
                                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                                    {reports.map((report) => (
                                                        <ReportCard
                                                            key={report._id}
                                                            report={report}
                                                            onUpdate={fetchUserData}
                                                        />
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default UserDashboard;
