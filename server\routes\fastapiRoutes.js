import express from 'express';
import { analyzeTumorFastAPI, getFastapiAnalysisHistory, getFastapiAnalysis, saveFlaskAnalysis, upload } from '../controllers/fastapiController.js';
import userAuth from '../middleware/userAuth.js';

const fastapiRouter = express.Router();

// Analyze tumor - supports both file upload and file path
fastapiRouter.post('/analyze', userAuth, upload.single('nifti_file'), analyzeTumorFastAPI);

// Save Flask API analysis results from frontend
fastapiRouter.post('/save-flask-results', userAuth, saveFlaskAnalysis);

// Get analysis history
fastapiRouter.get('/history', userAuth, getFastapiAnalysisHistory);

// Get specific analysis
fastapiRouter.get('/analysis/:analysisId', userAuth, getFastapiAnalysis);

export { fastapiRouter };
