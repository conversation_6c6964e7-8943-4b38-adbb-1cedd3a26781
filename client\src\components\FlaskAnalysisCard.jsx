import { useState } from 'react';
import { toast } from 'react-toastify';

const FlaskAnalysisCard = ({ analysis, onUpdate }) => {
    const [showDetails, setShowDetails] = useState(false);

    const formatDate = (dateString, includeTime = false) => {
        const date = new Date(dateString);
        const options = {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            ...(includeTime && {
                hour: '2-digit',
                minute: '2-digit'
            })
        };
        return date.toLocaleDateString('en-US', options);
    };

    const generatePDFReport = () => {
        const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Flask API Brain Tumor Analysis Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { text-align: center; border-bottom: 2px solid #3B82F6; padding-bottom: 20px; margin-bottom: 30px; }
        .header h1 { color: #1F2937; margin: 0; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #374151; border-bottom: 1px solid #E5E7EB; padding-bottom: 10px; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px; }
        .card { border: 1px solid #E5E7EB; border-radius: 8px; padding: 15px; }
        .card h3 { margin-top: 0; color: #374151; }
        .percentage { font-size: 1.2em; font-weight: bold; }
        .tumor-core { color: #DC2626; }
        .whole-tumor { color: #EA580C; }
        .enhancing-tumor { color: #D97706; }
        .confidence-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; }
        .confidence-card { text-align: center; background: #F9FAFB; padding: 15px; border-radius: 8px; }
        .confidence-score { font-size: 1.5em; font-weight: bold; color: #2563EB; }
        .stage-box { background: #FEF3C7; border: 1px solid #F59E0B; padding: 15px; border-radius: 8px; }
        .stage-text { font-weight: bold; color: #92400E; }
        .print-button { background: #3B82F6; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin-bottom: 20px; }
        @media print { .print-button { display: none; } }
    </style>
</head>
<body>
    <button class="print-button" onclick="window.print()">🖨️ Print as PDF</button>
    
    <div class="header">
        <h1>🧠 Flask API Brain Tumor Analysis Report</h1>
        <p><strong>Patient ID:</strong> ${analysis.patient_id}</p>
        <p><strong>Analysis Date:</strong> ${formatDate(analysis.createdAt, true)}</p>
    </div>

    <div class="section">
        <h2>📊 Tumor Analysis Results</h2>
        <div class="grid">
            <div class="card">
                <h3>Tumor Percentages</h3>
                <p><strong>Tumor Core:</strong> <span class="percentage tumor-core">${analysis.tumor_core}</span></p>
                <p><strong>Whole Tumor:</strong> <span class="percentage whole-tumor">${analysis.whole_tumor}</span></p>
                <p><strong>Enhancing Tumor:</strong> <span class="percentage enhancing-tumor">${analysis.enhancing_tumor}</span></p>
            </div>
            
            <div class="card">
                <h3>Volume Data (mm³)</h3>
                <p><strong>Brain Volume:</strong> ${analysis.volumes_mm3?.brain_volume || 'N/A'}</p>
                <p><strong>Tumor Core:</strong> ${analysis.volumes_mm3?.tumor_core || 'N/A'}</p>
                <p><strong>Whole Tumor:</strong> ${analysis.volumes_mm3?.whole_tumor || 'N/A'}</p>
                <p><strong>Enhancing Tumor:</strong> ${analysis.volumes_mm3?.enhancing_tumor || 'N/A'}</p>
            </div>
        </div>
    </div>

    ${analysis.confidence ? `
    <div class="section">
        <h2>🎯 Model Confidence Scores</h2>
        <div class="confidence-grid">
            <div class="confidence-card">
                <div>Enhancing Tumor</div>
                <div class="confidence-score">${(analysis.confidence.enhancing_tumor * 100).toFixed(1)}%</div>
            </div>
            <div class="confidence-card">
                <div>Tumor Core</div>
                <div class="confidence-score">${(analysis.confidence.tumor_core * 100).toFixed(1)}%</div>
            </div>
            <div class="confidence-card">
                <div>Whole Tumor</div>
                <div class="confidence-score">${(analysis.confidence.whole_tumor * 100).toFixed(1)}%</div>
            </div>
        </div>
    </div>
    ` : ''}

    ${analysis.stage ? `
    <div class="section">
        <h2>🏥 Stage Classification</h2>
        <div class="stage-box">
            <span class="stage-text">Brain Volume Stage: ${analysis.stage.brain_volume}</span>
        </div>
    </div>
    ` : ''}

    <div class="section">
        <h2>ℹ️ Analysis Information</h2>
        <p><strong>Analysis Method:</strong> Flask API Brain Tumor Segmentation</p>
        <p><strong>Generated:</strong> ${formatDate(analysis.createdAt, true)}</p>
        <p><strong>Patient ID:</strong> ${analysis.patient_id}</p>
    </div>
</body>
</html>`;

        const blob = new Blob([htmlContent], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `Flask_Analysis_${analysis.patient_id}_${formatDate(analysis.createdAt).replace(/\s+/g, '_')}.html`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        toast.success('Analysis report downloaded successfully!');
    };

    return (
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 group">
            {/* Header with gradient */}
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4 rounded-t-lg">
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                            <span className="text-white text-lg">🧠</span>
                        </div>
                        <div>
                            <h3 className="text-white font-semibold">Flask API Analysis</h3>
                            <p className="text-blue-100 text-sm">Patient: {analysis.patient_id}</p>
                        </div>
                    </div>
                    <div className="text-right">
                        <p className="text-blue-100 text-sm">{formatDate(analysis.createdAt, true)}</p>
                    </div>
                </div>
            </div>

            {/* Content */}
            <div className="p-6">
                {/* Quick Stats */}
                <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="text-center">
                        <p className="text-sm text-gray-600">Tumor Core</p>
                        <p className="text-lg font-bold text-red-600">{analysis.tumor_core}</p>
                    </div>
                    <div className="text-center">
                        <p className="text-sm text-gray-600">Whole Tumor</p>
                        <p className="text-lg font-bold text-orange-600">{analysis.whole_tumor}</p>
                    </div>
                    <div className="text-center">
                        <p className="text-sm text-gray-600">Enhancing Tumor</p>
                        <p className="text-lg font-bold text-yellow-600">{analysis.enhancing_tumor}</p>
                    </div>
                </div>

                {/* Stage Info */}
                {analysis.stage && (
                    <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-4">
                        <p className="text-sm text-gray-700">
                            <span className="font-medium">Stage:</span> 
                            <span className="font-bold text-orange-700 ml-1">{analysis.stage.brain_volume}</span>
                        </p>
                    </div>
                )}

                {/* Action Buttons */}
                <div className="flex space-x-3">
                    <button
                        onClick={() => setShowDetails(!showDetails)}
                        className="flex-1 bg-blue-50 text-blue-700 px-4 py-2 rounded-lg hover:bg-blue-100 transition-colors duration-200 text-sm font-medium"
                    >
                        {showDetails ? 'Hide Details' : 'View Details'}
                    </button>
                    <button
                        onClick={generatePDFReport}
                        className="flex-1 bg-green-50 text-green-700 px-4 py-2 rounded-lg hover:bg-green-100 transition-colors duration-200 text-sm font-medium"
                    >
                        📄 Download Report
                    </button>
                </div>

                {/* Detailed View */}
                {showDetails && (
                    <div className="mt-6 pt-6 border-t border-gray-200">
                        <div className="space-y-6">
                            {/* Volume Data */}
                            {analysis.volumes_mm3 && (
                                <div>
                                    <h4 className="font-semibold text-gray-900 mb-3">Volume Data (mm³)</h4>
                                    <div className="grid grid-cols-2 gap-3 text-sm">
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Brain Volume:</span>
                                            <span className="font-medium">{analysis.volumes_mm3.brain_volume}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Tumor Core:</span>
                                            <span className="font-medium">{analysis.volumes_mm3.tumor_core}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Whole Tumor:</span>
                                            <span className="font-medium">{analysis.volumes_mm3.whole_tumor}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Enhancing Tumor:</span>
                                            <span className="font-medium">{analysis.volumes_mm3.enhancing_tumor}</span>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Confidence Scores */}
                            {analysis.confidence && (
                                <div>
                                    <h4 className="font-semibold text-gray-900 mb-3">Model Confidence Scores</h4>
                                    <div className="grid grid-cols-3 gap-3">
                                        <div className="text-center bg-gray-50 p-3 rounded-lg">
                                            <p className="text-xs text-gray-600 mb-1">Enhancing Tumor</p>
                                            <p className="text-lg font-bold text-blue-600">
                                                {(analysis.confidence.enhancing_tumor * 100).toFixed(1)}%
                                            </p>
                                        </div>
                                        <div className="text-center bg-gray-50 p-3 rounded-lg">
                                            <p className="text-xs text-gray-600 mb-1">Tumor Core</p>
                                            <p className="text-lg font-bold text-blue-600">
                                                {(analysis.confidence.tumor_core * 100).toFixed(1)}%
                                            </p>
                                        </div>
                                        <div className="text-center bg-gray-50 p-3 rounded-lg">
                                            <p className="text-xs text-gray-600 mb-1">Whole Tumor</p>
                                            <p className="text-lg font-bold text-blue-600">
                                                {(analysis.confidence.whole_tumor * 100).toFixed(1)}%
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default FlaskAnalysisCard;
