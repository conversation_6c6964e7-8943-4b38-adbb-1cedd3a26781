// config/flaskApi.js
export const FLASK_API_CONFIG = {
    // Flask API Configuration
    baseUrl: import.meta.env.REACT_APP_FLASK_API_URL || 'http://127.0.0.1:8000',
    timeout: 300000, // 5 minutes for analysis
    healthCheckTimeout: 5000,
    
    // Endpoints
    endpoints: {
        analyze: '/analyze',
        health: '/'
    },
    
    // File requirements for BraTS analysis
    fileRequirements: {
        supportedFormats: ['.nii', '.nii.gz'],
        maxFileSize: 500 * 1024 * 1024, // 500MB
        requiredModalities: {
            flair: '_0000', // FLAIR image
            t1: '_0001',    // T1 image
            t1ce: '_0002',  // T1CE image
            t2: '_0003'     // T2 image
        }
    },
    
    // Analysis settings
    analysis: {
        pollInterval: 3000, // 3 seconds
        maxPollTime: 120000, // 2 minutes
        confidenceThreshold: 0.5,
        
        // Tumor severity thresholds (based on whole tumor percentage)
        severityThresholds: {
            low: 1,      // < 1%
            moderate: 3, // 1-3%
            high: 5,     // 3-5%
            critical: 5  // > 5%
        },
        
        // Brain volume stage thresholds (mm³)
        stageThresholds: {
            stage1: 1000000,  // < 1M mm³
            stage2: 1200000,  // 1M-1.2M mm³
            stage3: 1500000,  // 1.2M-1.5M mm³
            stage4: 1500000   // > 1.5M mm³
        }
    },
    
    // UI Configuration
    ui: {
        colors: {
            tumorCore: {
                bg: 'bg-red-50',
                text: 'text-red-600',
                border: 'border-red-200'
            },
            wholeTumor: {
                bg: 'bg-orange-50',
                text: 'text-orange-600',
                border: 'border-orange-200'
            },
            enhancingTumor: {
                bg: 'bg-yellow-50',
                text: 'text-yellow-600',
                border: 'border-yellow-200'
            },
            severity: {
                'No Tumor Detected': 'text-green-600',
                'Low': 'text-yellow-600',
                'Moderate': 'text-orange-600',
                'High': 'text-red-600',
                'Critical': 'text-red-800'
            },
            stage: {
                'Stage I': 'text-green-600',
                'Stage II': 'text-yellow-600',
                'Stage III': 'text-orange-600',
                'Stage IV': 'text-red-600'
            }
        }
    }
};

// Environment-specific overrides

export default FLASK_API_CONFIG;