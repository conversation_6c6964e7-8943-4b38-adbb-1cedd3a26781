import requests
import json

# Test the Flask server
def test_flask_server():
    url = "http://127.0.0.1:8000/"
    
    # Test data
    test_data = {
        "patient_id": "BRATS_486",
  "image": "D:/imagesTs/BRATS_502_0000.nii.gz"

    }
    
    headers = {
        "Content-Type": "application/json",
        "Origin": "http://localhost:5173"
    }
    
    try:
        print("Testing Flask server...")
        print(f"URL: {url}")
        print(f"Data: {test_data}")
        
        # Make the request
        response = requests.post(url, json=test_data, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print(f"Response: {response.text}")
        
        # Check CORS headers
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
        }
        
        print(f"CORS Headers: {cors_headers}")
        
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Make sure your Flask server is running on localhost:8000")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_flask_server()