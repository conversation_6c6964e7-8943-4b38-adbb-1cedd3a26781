/* eslint-disable react/prop-types */
import { useState, useContext } from 'react';
import { AppContent } from '../context/AppContext';
import axios from 'axios';
import { toast } from 'react-toastify';
import { format } from 'date-fns';
import NiftiViewer from './NiftiViewer';

const AnalysisResults = ({ upload }) => {
    const { backendUrl } = useContext(AppContent);
    const [analyzing, setAnalyzing] = useState(false);
    const [showDetails, setShowDetails] = useState(false);
    const [flaskResults, setFlaskResults] = useState(null);

    const handleAnalyze = async () => {
        try {
            setAnalyzing(true);

            // Extract patient ID from filename if available
            const patientId = upload.originalName ?
                upload.originalName.replace(/\.(nii|nii\.gz)$/, '').replace(/_\d{4}$/, '') :
                `PATIENT_${Date.now()}`;

            // Fetch the file from the static file server
            const fileUrl = `${backendUrl}/${upload.filePath}`;
            const fileResponse = await axios.get(fileUrl, {
                responseType: 'blob'
            });

            // Create FormData with the actual file for Flask API
            const formData = new FormData();
            formData.append('patient_id', patientId);
            formData.append('nifti_file', fileResponse.data, upload.originalName);

            console.log('Sending request to Flask API...');
            const response = await axios.post(
                'http://localhost:8001/analyze',
                formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data'
                    },
                    timeout: 300000, // 5 minutes
                    withCredentials: false // Disable credentials for CORS
                }
            );
            console.log('Flask API response received:', response.data);

            // Extract results from Flask API response
            const analysisResults = response.data.results || response.data;

            if (analysisResults && analysisResults.tumor_core) {
                setFlaskResults(analysisResults);
                toast.success('Flask API analysis completed successfully!');
            } else {
                toast.error(response.data.error || response.data.message || 'Flask API analysis failed');
            }
        } catch (error) {
            console.error('Flask API Analysis error:', error);

            // Enhanced error handling
            let errorMessage = 'Flask API analysis failed';
            if (error.code === 'ECONNREFUSED') {
                errorMessage = 'Flask API server is not running. Please start the Flask server on port 8001.';
            } else if (error.code === 'ECONNABORTED') {
                errorMessage = 'Analysis timeout. Please try again.';
            } else if (error.response) {
                const errorData = error.response.data;
                errorMessage = errorData?.error ||
                             errorData?.message ||
                             errorData?.detail ||
                             `Server error: ${error.response.status}`;
            } else if (error.request) {
                errorMessage = 'Network error. Please check your connection.';
            }

            toast.error(errorMessage);
        } finally {
            setAnalyzing(false);
        }
    };





    const getStatusColor = (status) => {
        switch (status) {
            case 'uploaded': return 'bg-gray-100 text-gray-800';
            case 'processing': return 'bg-yellow-100 text-yellow-800';
            case 'analyzed': return 'bg-green-100 text-green-800';
            case 'error': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getTumorLevelColor = (level) => {
        switch (level) {
            case 'Low': return 'text-green-600';
            case 'Medium': return 'text-yellow-600';
            case 'High': return 'text-orange-600';
            case 'Critical': return 'text-red-600';
            default: return 'text-gray-600';
        }
    };

    return (
        <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
            <div className="flex items-center justify-between mb-4">
                <div>
                    <h3 className="text-lg font-medium text-gray-900">{upload.originalName}</h3>
                    <p className="text-sm text-gray-500">
                        Uploaded on {format(new Date(upload.uploadDate), 'MMM dd, yyyy HH:mm')}
                    </p>
                </div>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(upload.status)}`}>
                    {upload.status.charAt(0).toUpperCase() + upload.status.slice(1)}
                </span>
            </div>

            {/* File Info */}
            <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                <div>
                    <span className="text-gray-500">File Size:</span>
                    <span className="ml-2 font-medium">{(upload.fileSize / (1024 * 1024)).toFixed(2)} MB</span>
                </div>
                <div>
                    <span className="text-gray-500">Format:</span>
                    <span className="ml-2 font-medium">
                        {upload.fileFormat ||
                         (upload.originalName?.toLowerCase().endsWith('.nii.gz') ? 'NIfTI (Compressed)' :
                          upload.originalName?.toLowerCase().endsWith('.nii') ? 'NIfTI' :
                          upload.originalName?.toLowerCase().endsWith('.dcm') ? 'DICOM' :
                          upload.mimeType)}
                    </span>
                </div>
            </div>

            {/* NIfTI File Viewer */}
            {(upload.originalName?.toLowerCase().endsWith('.nii') || upload.originalName?.toLowerCase().endsWith('.nii.gz')) && (
                <div className="mb-4">
                    <NiftiViewer
                        filePath={upload.filePath}
                        fileName={upload.originalName}
                    />
                </div>
            )}



            {/* Action Buttons */}
            <div className="flex space-x-3 mb-4">
                {upload.status === 'uploaded' && (
                    <button
                        onClick={handleAnalyze}
                        disabled={analyzing}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                        {analyzing ? (
                            <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                Flask API Analyzing...
                            </>
                        ) : (
                            <>
                                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                </svg>
                                ⚡ Start Flask API Analysis
                            </>
                        )}
                    </button>
                )}

                {upload.status === 'analyzed' && upload.analysisId && !upload.reportId && (
                    <button
                        onClick={handleGenerateReport}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                    >
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Generate Report
                    </button>
                )}

                {upload.status === 'analyzed' && upload.analysisId && (
                    <button
                        onClick={() => setShowDetails(!showDetails)}
                        className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        {showDetails ? 'Hide' : 'View'} Results
                    </button>
                )}
            </div>

            {/* Analysis Results */}
            {showDetails && upload.analysisId && (
                <div className="border-t border-gray-200 pt-4">
                    <h4 className="text-md font-medium text-gray-900 mb-3">Analysis Results</h4>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-3">
                            <div className="flex justify-between">
                                <span className="text-sm text-gray-500">Tumor Detected:</span>
                                <span className={`text-sm font-medium ${upload.analysisId.tumorDetected ? 'text-red-600' : 'text-green-600'}`}>
                                    {upload.analysisId.tumorDetected ? 'Yes' : 'No'}
                                </span>
                            </div>
                            
                            {upload.analysisId.tumorDetected && (
                                <>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-500">Tumor Level:</span>
                                        <span className={`text-sm font-medium ${getTumorLevelColor(upload.analysisId.tumorLevel)}`}>
                                            {upload.analysisId.tumorLevel}
                                        </span>
                                    </div>
                                    
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-500">Tumor Grade:</span>
                                        <span className="text-sm font-medium text-gray-900">
                                            {upload.analysisId.tumorGrade}
                                        </span>
                                    </div>
                                    
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-500">Tumor Type:</span>
                                        <span className="text-sm font-medium text-gray-900">
                                            {upload.analysisId.tumorType}
                                        </span>
                                    </div>
                                </>
                            )}
                        </div>
                        
                        <div className="space-y-3">
                            <div className="flex justify-between">
                                <span className="text-sm text-gray-500">Confidence Score:</span>
                                <span className="text-sm font-medium text-gray-900">
                                    {upload.analysisId.confidenceScore}%
                                </span>
                            </div>
                            
                            <div className="flex justify-between">
                                <span className="text-sm text-gray-500">Number of Slices:</span>
                                <span className="text-sm font-medium text-gray-900">
                                    {upload.analysisId.numberOfSlices}
                                </span>
                            </div>
                            
                            {upload.analysisId.tumorDetected && (
                                <>
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-500">Voxels:</span>
                                        <span className="text-sm font-medium text-gray-900">
                                            {upload.analysisId.numberOfVoxels?.toLocaleString()}
                                        </span>
                                    </div>
                                    
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-500">Volume:</span>
                                        <span className="text-sm font-medium text-gray-900">
                                            {upload.analysisId.tumorVolume} mm³
                                        </span>
                                    </div>
                                </>
                            )}
                        </div>
                    </div>

                    {upload.analysisId.recommendations && (
                        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                            <h5 className="text-sm font-medium text-blue-900 mb-1">Recommendations:</h5>
                            <p className="text-sm text-blue-800">{upload.analysisId.recommendations}</p>
                        </div>
                    )}
                </div>
            )}

            {/* Flask API Results Display */}
            {flaskResults && (
                <div className="border-t border-gray-200 pt-4 mt-4">
                    <h4 className="text-md font-medium text-gray-900 mb-3 flex items-center">
                        <span className="mr-2">⚡</span>
                        Flask API Analysis Results
                    </h4>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        {[
                            { key: 'tumor_core', label: 'Tumor Core', icon: '🎯', color: 'blue' },
                            { key: 'whole_tumor', label: 'Whole Tumor', icon: '🧠', color: 'green' },
                            { key: 'enhancing_tumor', label: 'Enhancing Tumor', icon: '⚡', color: 'yellow' }
                        ].map((item) => (
                            <div key={item.key} className="bg-white border border-gray-200 rounded-lg p-4">
                                <div className="flex items-center space-x-2 mb-3">
                                    <span className="text-2xl">{item.icon}</span>
                                    <h5 className="font-semibold text-gray-900">{item.label}</h5>
                                </div>

                                <div className="space-y-2">
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Percentage:</span>
                                        <span className="font-bold text-lg">{flaskResults[item.key]}</span>
                                    </div>

                                    {flaskResults.volumes_mm3 && (
                                        <div className="flex justify-between">
                                            <span className="text-sm text-gray-600">Volume:</span>
                                            <span className="font-medium">{flaskResults.volumes_mm3[item.key]}</span>
                                        </div>
                                    )}

                                    {flaskResults.confidence && flaskResults.confidence[item.key] !== null && (
                                        <div className="flex justify-between">
                                            <span className="text-sm text-gray-600">Confidence:</span>
                                            <span className="font-medium text-green-600">
                                                {(flaskResults.confidence[item.key] * 100).toFixed(1)}%
                                            </span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>

                    {/* Stage and Brain Volume */}
                    {flaskResults.stage && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="bg-white border border-gray-200 rounded-lg p-4">
                                <h5 className="font-semibold text-gray-900 mb-3">Tumor Stage</h5>
                                <div className="inline-flex px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                    {flaskResults.stage.brain_volume}
                                </div>
                            </div>

                            {flaskResults.volumes_mm3 && (
                                <div className="bg-white border border-gray-200 rounded-lg p-4">
                                    <h5 className="font-semibold text-gray-900 mb-3">Brain Volume</h5>
                                    <p className="text-lg font-bold text-gray-900">
                                        {flaskResults.volumes_mm3.brain_volume}
                                    </p>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            )}

            {upload.status === 'processing' && (
                <div className="border-t border-gray-200 pt-4">
                    <div className="flex items-center space-x-3">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
                        <span className="text-sm text-gray-600">Analysis in progress... This may take a few minutes.</span>
                    </div>
                </div>
            )}
        </div>
    );
};

export default AnalysisResults;