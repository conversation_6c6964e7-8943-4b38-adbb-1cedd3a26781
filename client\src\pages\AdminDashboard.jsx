import { useState, useEffect, useContext } from 'react';
import { AppContent } from '../context/AppContext';
import axios from 'axios';
import { toast } from 'react-toastify';
import Navbar from '../components/Navbar';
import { format } from 'date-fns';

const AdminDashboard = () => {
    const { backendUrl, userData } = useContext(AppContent);
    const [stats, setStats] = useState(null);
    const [users, setUsers] = useState([]);
    const [reports, setReports] = useState([]);
    const [flaskAnalyses, setFlaskAnalyses] = useState([]);
    const [loading, setLoading] = useState(true);
    const [activeTab, setActiveTab] = useState('overview');
    const [userFilter, setUserFilter] = useState('all');
    const [reportFilter, setReportFilter] = useState('all');
    const [error, setError] = useState(null);

    // Early return if userData is not available
    if (!userData) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
                <p className="ml-4 text-gray-600">Loading user data...</p>
            </div>
        );
    }

    // Check if user is admin
    if (userData.role !== 'admin') {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
                    <p className="text-gray-600">You don't have permission to access this page.</p>
                </div>
            </div>
        );
    }

    useEffect(() => {
        fetchDashboardData();
    }, []);

    const fetchDashboardData = async () => {
        try {
            setLoading(true);
            setError(null);

            // Check if backendUrl is available
            if (!backendUrl) {
                throw new Error('Backend URL not configured');
            }

            // Get authentication token
            const token = localStorage.getItem('token');
            const authHeaders = {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            };

            // Fetch dashboard stats
            try {
                const statsResponse = await axios.get(`${backendUrl}/api/admin/dashboard`, {
                    headers: authHeaders
                });
                if (statsResponse.data.success) {
                    setStats(statsResponse.data.stats);
                }
            } catch (statsError) {
                console.error('Error fetching stats:', statsError);
                setStats(null);
            }

            // Fetch users
            try {
                const usersResponse = await axios.get(`${backendUrl}/api/admin/users`, {
                    headers: authHeaders
                });
                if (usersResponse.data.success) {
                    setUsers(usersResponse.data.users || []);
                }
            } catch (usersError) {
                console.error('Error fetching users:', usersError);
                setUsers([]);
            }

            // Fetch reports
            try {
                const reportsResponse = await axios.get(`${backendUrl}/api/admin/reports`, {
                    headers: authHeaders
                });
                if (reportsResponse.data.success) {
                    setReports(reportsResponse.data.reports || []);
                }
            } catch (reportsError) {
                console.error('Error fetching reports:', reportsError);
                setReports([]);
            }

            // Fetch Flask API analyses
            try {
                console.log('🔄 Admin: Fetching Flask API analyses from backend...');
                console.log('🔑 Admin token found:', !!token);

                const flaskResponse = await axios.get(`${backendUrl}/api/admin/flask-analyses`, {
                    headers: authHeaders
                });
                console.log('📥 Admin Flask response:', flaskResponse.data);
                if (flaskResponse.data.success) {
                    console.log('✅ Admin Flask analyses found:', flaskResponse.data.analyses.length);
                    setFlaskAnalyses(flaskResponse.data.analyses || []);
                } else {
                    console.log('❌ Admin Flask response not successful:', flaskResponse.data.message);
                }
            } catch (flaskError) {
                console.error('❌ Error fetching Flask analyses:', flaskError);
                console.error('Error details:', flaskError.response?.data);
                setFlaskAnalyses([]);
            }

        } catch (error) {
            setError(error.message);
            toast.error('Error fetching dashboard data: ' + error.message);
            console.error('Dashboard data fetch error:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleDoctorApproval = async (doctorId, isApproved) => {
        try {
            console.log('Updating doctor status:', { doctorId, isApproved, backendUrl });

            const response = await axios.put(`${backendUrl}/api/admin/doctor/${doctorId}/status`, {
                isApproved
            });

            console.log('Response:', response.data);

            if (response.data.success) {
                toast.success(`Doctor ${isApproved ? 'approved' : 'rejected'} successfully`);
                fetchDashboardData();
            } else {
                toast.error(response.data.message || 'Failed to update doctor status');
            }
        } catch (error) {
            console.error('Doctor status update error:', error);

            if (error.response) {
                // Server responded with error status
                const message = error.response.data?.message || 'Server error';
                const status = error.response.status;
                toast.error(`Error ${status}: ${message}`);
                console.error('Server error details:', error.response.data);
            } else if (error.request) {
                // Request made but no response
                toast.error('Network error: Unable to reach server');
                console.error('Network error:', error.request);
            } else {
                // Something else happened
                toast.error('Unexpected error occurred');
                console.error('Unexpected error:', error.message);
            }
        }
    };

    const handleDeleteUser = async (userId, userName, userRole) => {
        // Confirm deletion
        if (!window.confirm(`Are you sure you want to delete ${userRole} "${userName}"? This action cannot be undone.`)) {
            return;
        }

        try {
            console.log('Deleting user:', { userId, userName, userRole });

            const response = await axios.delete(`${backendUrl}/api/admin/user/${userId}`);

            console.log('Delete response:', response.data);

            if (response.data.success) {
                toast.success(response.data.message);
                fetchDashboardData();
            } else {
                toast.error(response.data.message || 'Failed to delete user');
            }
        } catch (error) {
            console.error('User deletion error:', error);

            if (error.response) {
                const message = error.response.data?.message || 'Server error';
                const status = error.response.status;
                toast.error(`Error ${status}: ${message}`);
                console.error('Server error details:', error.response.data);
            } else if (error.request) {
                toast.error('Network error: Unable to reach server');
                console.error('Network error:', error.request);
            } else {
                toast.error('Unexpected error occurred');
                console.error('Unexpected error:', error.message);
            }
        }
    };

    const filteredUsers = users.filter(user => {
        if (!user || !user.role) return false;
        if (userFilter === 'all') return true;
        return user.role === userFilter;
    });

    const filteredReports = reports.filter(report => {
        if (!report || !report.status) return false;
        if (reportFilter === 'all') return true;
        return report.status === reportFilter;
    });

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50">
                <Navbar />
                <div className="flex justify-center items-center h-96">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"></div>
                        <p className="text-gray-600">Loading admin dashboard...</p>
                    </div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen bg-gray-50">
                <Navbar />
                <div className="flex justify-center items-center h-96">
                    <div className="text-center">
                        <div className="text-red-600 text-xl mb-4">⚠️ Error Loading Dashboard</div>
                        <p className="text-gray-600 mb-4">{error}</p>
                        <button
                            onClick={fetchDashboardData}
                            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                        >
                            Retry
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            <Navbar />
            
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* Header */}
                <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">
                        Admin Dashboard
                    </h1>
                    <p className="text-gray-600">
                        Welcome back, {userData?.name}! Manage users, doctors, and monitor system activity.
                    </p>
                </div>

                {/* Tab Navigation */}
                <div className="bg-white rounded-lg shadow-sm mb-8">
                    <div className="border-b border-gray-200">
                        <nav className="-mb-px flex space-x-8 px-6">
                            <button
                                onClick={() => setActiveTab('overview')}
                                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                    activeTab === 'overview'
                                        ? 'border-blue-500 text-blue-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                            >
                                Overview
                            </button>
                            <button
                                onClick={() => setActiveTab('users')}
                                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                    activeTab === 'users'
                                        ? 'border-blue-500 text-blue-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                            >
                                Users & Doctors
                            </button>
                            <button
                                onClick={() => setActiveTab('reports')}
                                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                    activeTab === 'reports'
                                        ? 'border-blue-500 text-blue-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                            >
                                Reports
                            </button>
                            <button
                                onClick={() => setActiveTab('flask-analyses')}
                                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                                    activeTab === 'flask-analyses'
                                        ? 'border-blue-500 text-blue-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                            >
                                Flask API Analyses
                            </button>
                        </nav>
                    </div>

                    <div className="p-6">
                        {activeTab === 'overview' && (
                            <div className="space-y-6">
                                {stats ? (
                                    <>
                                        {/* Stats Grid */}
                                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                            <div className="bg-blue-50 rounded-lg p-6">
                                                <div className="flex items-center">
                                                    <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                                                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                                        </svg>
                                                    </div>
                                                    <div className="ml-4">
                                                        <p className="text-sm font-medium text-blue-600">Total Users</p>
                                                        <p className="text-2xl font-bold text-blue-900">{stats.users?.total || 0}</p>
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="bg-green-50 rounded-lg p-6">
                                                <div className="flex items-center">
                                                    <div className="p-3 rounded-full bg-green-100 text-green-600">
                                                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                                        </svg>
                                                    </div>
                                                    <div className="ml-4">
                                                        <p className="text-sm font-medium text-green-600">Approved Doctors</p>
                                                        <p className="text-2xl font-bold text-green-900">{stats.users?.approvedDoctors || 0}</p>
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="bg-yellow-50 rounded-lg p-6">
                                                <div className="flex items-center">
                                                    <div className="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                        </svg>
                                                    </div>
                                                    <div className="ml-4">
                                                        <p className="text-sm font-medium text-yellow-600">Pending Doctors</p>
                                                        <p className="text-2xl font-bold text-yellow-900">{stats.users?.pendingDoctors || 0}</p>
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="bg-purple-50 rounded-lg p-6">
                                                <div className="flex items-center">
                                                    <div className="p-3 rounded-full bg-purple-100 text-purple-600">
                                                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                        </svg>
                                                    </div>
                                                    <div className="ml-4">
                                                        <p className="text-sm font-medium text-purple-600">Total Reports</p>
                                                        <p className="text-2xl font-bold text-purple-900">{stats.reports?.total || 0}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Analysis Stats */}
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div className="bg-white border border-gray-200 rounded-lg p-6">
                                                <h3 className="text-lg font-medium text-gray-900 mb-4">Upload & Analysis Stats</h3>
                                                <div className="space-y-3">
                                                    <div className="flex justify-between">
                                                        <span className="text-gray-600">Total Uploads:</span>
                                                        <span className="font-medium">{stats.uploads?.total || 0}</span>
                                                    </div>
                                                    <div className="flex justify-between">
                                                        <span className="text-gray-600">Analyzed:</span>
                                                        <span className="font-medium">{stats.uploads?.analyzed || 0}</span>
                                                    </div>
                                                    <div className="flex justify-between">
                                                        <span className="text-gray-600">Tumor Detected:</span>
                                                        <span className="font-medium text-red-600">{stats.analysis?.tumorDetected || 0}</span>
                                                    </div>
                                                    <div className="flex justify-between">
                                                        <span className="text-gray-600">No Tumor:</span>
                                                        <span className="font-medium text-green-600">{stats.analysis?.noTumorDetected || 0}</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="bg-white border border-gray-200 rounded-lg p-6">
                                                <h3 className="text-lg font-medium text-gray-900 mb-4">Report Status</h3>
                                                <div className="space-y-3">
                                                    <div className="flex justify-between">
                                                        <span className="text-gray-600">Verified:</span>
                                                        <span className="font-medium text-green-600">{stats.reports?.verified || 0}</span>
                                                    </div>
                                                    <div className="flex justify-between">
                                                        <span className="text-gray-600">Pending:</span>
                                                        <span className="font-medium text-yellow-600">{stats.reports?.pending || 0}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Flask API Analysis Stats */}
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                                            <div className="bg-white border border-gray-200 rounded-lg p-6">
                                                <h3 className="text-lg font-medium text-gray-900 mb-4">Flask API Analysis</h3>
                                                <div className="space-y-3">
                                                    <div className="flex justify-between">
                                                        <span className="text-gray-600">Total Analyses:</span>
                                                        <span className="font-medium text-blue-600">{stats.flaskAnalysis?.total || 0}</span>
                                                    </div>
                                                    <div className="flex justify-between">
                                                        <span className="text-gray-600">Today:</span>
                                                        <span className="font-medium text-purple-600">{stats.flaskAnalysis?.today || 0}</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="bg-white border border-gray-200 rounded-lg p-6">
                                                <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
                                                <div className="space-y-3">
                                                    <div className="flex justify-between">
                                                        <span className="text-gray-600">Flask Analyses:</span>
                                                        <span className="font-medium">{flaskAnalyses.length}</span>
                                                    </div>
                                                    <div className="flex justify-between">
                                                        <span className="text-gray-600">Traditional Reports:</span>
                                                        <span className="font-medium">{reports.length}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </>
                                ) : (
                                    <div className="text-center py-8">
                                        <p className="text-gray-500">Loading dashboard statistics...</p>
                                    </div>
                                )}
                            </div>
                        )}

                        {activeTab === 'users' && (
                            <div className="space-y-6">
                                <div className="flex justify-between items-center">
                                    <h3 className="text-lg font-medium text-gray-900">Users & Doctors Management</h3>
                                    <select
                                        value={userFilter}
                                        onChange={(e) => setUserFilter(e.target.value)}
                                        className="px-3 py-2 border border-gray-300 rounded-md"
                                    >
                                        <option value="all">All Users</option>
                                        <option value="user">Users</option>
                                        <option value="doctor">Doctors</option>
                                        <option value="admin">Admins</option>
                                    </select>
                                </div>

                                <div className="medical-card overflow-hidden">
                                    <table className="min-w-full divide-y divide-blue-200">
                                        <thead className="bg-gradient-to-r from-blue-50 to-indigo-50">
                                            <tr>
                                                <th className="px-6 py-4 text-left text-xs font-semibold text-blue-700 uppercase tracking-wider">
                                                    <div className="flex items-center space-x-2">
                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                                        </svg>
                                                        <span>User</span>
                                                    </div>
                                                </th>
                                                <th className="px-6 py-4 text-left text-xs font-semibold text-blue-700 uppercase tracking-wider">
                                                    <div className="flex items-center space-x-2">
                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m8 6V8a2 2 0 00-2-2H10a2 2 0 00-2 2v4.01" />
                                                        </svg>
                                                        <span>Role</span>
                                                    </div>
                                                </th>
                                                <th className="px-6 py-4 text-left text-xs font-semibold text-blue-700 uppercase tracking-wider">
                                                    <div className="flex items-center space-x-2">
                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                        </svg>
                                                        <span>Status</span>
                                                    </div>
                                                </th>
                                                <th className="px-6 py-4 text-left text-xs font-semibold text-blue-700 uppercase tracking-wider">
                                                    <div className="flex items-center space-x-2">
                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a4 4 0 118 0v4m-4 8a4 4 0 11-8 0v-1a4 4 0 014-4h4a4 4 0 014 4v1a4 4 0 11-8 0z" />
                                                        </svg>
                                                        <span>Joined</span>
                                                    </div>
                                                </th>
                                                <th className="px-6 py-4 text-left text-xs font-semibold text-blue-700 uppercase tracking-wider">
                                                    <div className="flex items-center space-x-2">
                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                                                        </svg>
                                                        <span>Actions</span>
                                                    </div>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-blue-100">
                                            {filteredUsers.map((user) => {
                                                if (!user || !user._id) return null;

                                                return (
                                                    <tr key={user._id} className="hover:bg-blue-50 transition-colors duration-200">
                                                        <td className="px-6 py-4 whitespace-nowrap">
                                                            <div>
                                                                <div className="text-sm font-medium text-gray-900">{user.name || 'Unknown User'}</div>
                                                                <div className="text-sm text-gray-500">{user.email || 'No email'}</div>
                                                                {user.specialization && (
                                                                    <div className="text-xs text-blue-600">{user.specialization}</div>
                                                                )}
                                                            </div>
                                                        </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                                            user.role === 'admin' ? 'bg-red-100 text-red-800' :
                                                            user.role === 'doctor' ? 'bg-blue-100 text-blue-800' :
                                                            'bg-green-100 text-green-800'
                                                        }`}>
                                                            {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                                                        </span>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="flex flex-col space-y-2">
                                                            <span className={`inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full transition-all duration-200 ${
                                                                user.isAccountVerified
                                                                    ? 'bg-green-100 text-green-800 border border-green-200 shadow-sm'
                                                                    : 'bg-yellow-100 text-yellow-800 border border-yellow-200 shadow-sm'
                                                            }`}>
                                                                <svg className={`w-3 h-3 mr-1 ${user.isAccountVerified ? 'text-green-600' : 'text-yellow-600'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    {user.isAccountVerified ? (
                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                    ) : (
                                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                    )}
                                                                </svg>
                                                                {user.isAccountVerified ? 'Verified' : 'Unverified'}
                                                            </span>
                                                            {user.role === 'doctor' && (
                                                                <span className={`inline-flex items-center px-2.5 py-1 text-xs font-medium rounded-full transition-all duration-200 ${
                                                                    user.isApproved
                                                                        ? 'bg-green-100 text-green-800 border border-green-200 shadow-sm'
                                                                        : 'bg-red-100 text-red-800 border border-red-200 shadow-sm'
                                                                }`}>
                                                                    <svg className={`w-3 h-3 mr-1 ${user.isApproved ? 'text-green-600' : 'text-red-600'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        {user.isApproved ? (
                                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                                                        ) : (
                                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                                        )}
                                                                    </svg>
                                                                    {user.isApproved ? 'Approved' : 'Pending'}
                                                                </span>
                                                            )}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                        {format(new Date(user.createdAt), 'MMM dd, yyyy')}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                        <div className="flex space-x-2">
                                                            {/* Doctor approval/rejection buttons */}
                                                            {user.role === 'doctor' && (
                                                                <div className="flex space-x-2">
                                                                    {/* Approve Button */}
                                                                    <button
                                                                        onClick={() => handleDoctorApproval(user._id, true)}
                                                                        disabled={user.isApproved}
                                                                        className={`group relative overflow-hidden px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-300 transform hover:scale-105 ${
                                                                            user.isApproved
                                                                                ? 'bg-green-100 text-green-800 border border-green-300 cursor-not-allowed'
                                                                                : 'bg-white text-green-600 border border-green-300 hover:bg-green-50 hover:text-green-700 hover:border-green-400 shadow-sm hover:shadow-md'
                                                                        }`}
                                                                    >
                                                                        <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-500 opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                                                                        <div className="relative flex items-center space-x-1">
                                                                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                                                            </svg>
                                                                            <span>{user.isApproved ? 'Approved' : 'Approve'}</span>
                                                                        </div>
                                                                    </button>

                                                                    {/* Reject Button */}
                                                                    <button
                                                                        onClick={() => handleDoctorApproval(user._id, false)}
                                                                        disabled={!user.isApproved}
                                                                        className={`group relative overflow-hidden px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-300 transform hover:scale-105 ${
                                                                            !user.isApproved
                                                                                ? 'bg-red-100 text-red-800 border border-red-300 cursor-not-allowed'
                                                                                : 'bg-white text-red-600 border border-red-300 hover:bg-red-50 hover:text-red-700 hover:border-red-400 shadow-sm hover:shadow-md'
                                                                        }`}
                                                                    >
                                                                        <div className="absolute inset-0 bg-gradient-to-r from-red-500 to-rose-500 opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                                                                        <div className="relative flex items-center space-x-1">
                                                                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                                                            </svg>
                                                                            <span>{!user.isApproved ? 'Rejected' : 'Reject'}</span>
                                                                        </div>
                                                                    </button>
                                                                </div>
                                                            )}

                                                            {/* Delete button for all users except current admin */}
                                                            {user.role !== 'admin' && (
                                                                <button
                                                                    onClick={() => handleDeleteUser(user._id, user.name, user.role)}
                                                                    className="group relative overflow-hidden px-3 py-1.5 text-xs font-medium rounded-lg transition-all duration-300 transform hover:scale-105 bg-white text-red-600 border border-red-300 hover:bg-red-50 hover:text-red-700 hover:border-red-400 shadow-sm hover:shadow-md"
                                                                >
                                                                    <div className="absolute inset-0 bg-gradient-to-r from-red-500 to-rose-500 opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                                                                    <div className="relative flex items-center space-x-1">
                                                                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                                        </svg>
                                                                        <span>Delete</span>
                                                                    </div>
                                                                </button>
                                                            )}
                                                        </div>
                                                    </td>
                                                </tr>
                                                );
                                            })}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        )}

                        {activeTab === 'reports' && (
                            <div className="space-y-6">
                                <div className="flex justify-between items-center">
                                    <h3 className="text-lg font-medium text-gray-900">Reports Management</h3>
                                    <select
                                        value={reportFilter}
                                        onChange={(e) => setReportFilter(e.target.value)}
                                        className="px-3 py-2 border border-gray-300 rounded-md"
                                    >
                                        <option value="all">All Reports</option>
                                        <option value="generated">Generated</option>
                                        <option value="assigned">Assigned</option>
                                        <option value="under_review">Under Review</option>
                                        <option value="verified">Verified</option>
                                        <option value="rejected">Rejected</option>
                                    </select>
                                </div>

                                {filteredReports.length === 0 ? (
                                    <div className="text-center py-8">
                                        <p className="text-gray-500">No reports found</p>
                                    </div>
                                ) : (
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                        {filteredReports.map((report) => {
                                            if (!report || !report._id) return null;

                                            return (
                                                <div key={report._id} className="bg-white border border-gray-200 rounded-lg p-6">
                                                    <div className="flex justify-between items-start mb-3">
                                                        <h4 className="text-sm font-medium text-gray-900 truncate">
                                                            {report.reportTitle || 'Untitled Report'}
                                                        </h4>
                                                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                                            report.status === 'verified' ? 'bg-green-100 text-green-800' :
                                                            report.status === 'rejected' ? 'bg-red-100 text-red-800' :
                                                            report.status === 'under_review' ? 'bg-purple-100 text-purple-800' :
                                                            report.status === 'assigned' ? 'bg-yellow-100 text-yellow-800' :
                                                            'bg-blue-100 text-blue-800'
                                                        }`}>
                                                            {report.status ? report.status.replace('_', ' ') : 'Unknown'}
                                                        </span>
                                                    </div>

                                                    <div className="space-y-2 text-sm">
                                                        <div>
                                                            <span className="text-gray-500">Patient:</span>
                                                            <span className="ml-2 font-medium">
                                                                {report.userId?.name || 'Unknown Patient'}
                                                            </span>
                                                        </div>
                                                        {report.assignedDoctorId && (
                                                            <div>
                                                                <span className="text-gray-500">Doctor:</span>
                                                                <span className="ml-2 font-medium">
                                                                    Dr. {report.assignedDoctorId.name || 'Unknown Doctor'}
                                                                </span>
                                                            </div>
                                                        )}
                                                        <div>
                                                            <span className="text-gray-500">Priority:</span>
                                                            <span className={`ml-2 font-medium ${
                                                                report.priority === 'Urgent' ? 'text-red-600' :
                                                                report.priority === 'High' ? 'text-orange-600' :
                                                                report.priority === 'Medium' ? 'text-yellow-600' :
                                                                'text-green-600'
                                                            }`}>
                                                                {report.priority || 'Standard'}
                                                            </span>
                                                        </div>
                                                        <div>
                                                            <span className="text-gray-500">Generated:</span>
                                                            <span className="ml-2 text-gray-900">
                                                                {report.generatedDate ?
                                                                    format(new Date(report.generatedDate), 'MMM dd, yyyy') :
                                                                    'Unknown Date'
                                                                }
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            );
                                        })}
                                    </div>
                                )}
                            </div>
                        )}

                        {activeTab === 'flask-analyses' && (
                            <div className="space-y-6">
                                <div className="flex justify-between items-center">
                                    <h3 className="text-lg font-medium text-gray-900">Flask API Analysis Results</h3>
                                    <div className="text-sm text-gray-500">
                                        Total: {flaskAnalyses.length} analyses
                                    </div>
                                </div>

                                {flaskAnalyses.length === 0 ? (
                                    <div className="text-center py-8">
                                        <p className="text-gray-500">No Flask API analyses found.</p>
                                    </div>
                                ) : (
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                        {flaskAnalyses.map((analysis) => (
                                            <div key={analysis._id} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                                                <div className="flex justify-between items-start mb-4">
                                                    <h4 className="text-lg font-medium text-gray-900">
                                                        Flask API Analysis
                                                    </h4>
                                                    <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                                                        {analysis.patient_id}
                                                    </span>
                                                </div>

                                                <div className="space-y-3">
                                                    <div className="grid grid-cols-3 gap-2 text-sm">
                                                        <div className="text-center">
                                                            <div className="font-medium text-red-600">{analysis.tumor_core}</div>
                                                            <div className="text-gray-500">Tumor Core</div>
                                                        </div>
                                                        <div className="text-center">
                                                            <div className="font-medium text-orange-600">{analysis.whole_tumor}</div>
                                                            <div className="text-gray-500">Whole Tumor</div>
                                                        </div>
                                                        <div className="text-center">
                                                            <div className="font-medium text-yellow-600">{analysis.enhancing_tumor}</div>
                                                            <div className="text-gray-500">Enhancing</div>
                                                        </div>
                                                    </div>

                                                    <div className="border-t pt-3">
                                                        <div className="flex justify-between text-sm">
                                                            <span className="text-gray-600">Patient:</span>
                                                            <span className="font-medium">{analysis.userId?.name || 'Unknown'}</span>
                                                        </div>
                                                        <div className="flex justify-between text-sm">
                                                            <span className="text-gray-600">Email:</span>
                                                            <span className="font-medium">{analysis.userId?.email || 'Unknown'}</span>
                                                        </div>
                                                        <div className="flex justify-between text-sm">
                                                            <span className="text-gray-600">Date:</span>
                                                            <span className="font-medium">
                                                                {analysis.createdAt ?
                                                                    format(new Date(analysis.createdAt), 'MMM dd, yyyy HH:mm') :
                                                                    'Unknown'
                                                                }
                                                            </span>
                                                        </div>
                                                    </div>

                                                    {analysis.stage?.brain_volume && (
                                                        <div className="border-t pt-3">
                                                            <div className="text-sm">
                                                                <span className="text-gray-600">Stage: </span>
                                                                <span className="font-medium text-purple-600">
                                                                    {analysis.stage.brain_volume}
                                                                </span>
                                                            </div>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AdminDashboard;
