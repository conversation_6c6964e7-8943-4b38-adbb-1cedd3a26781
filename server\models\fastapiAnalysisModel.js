import mongoose from "mongoose";

const fastapiAnalysisSchema = new mongoose.Schema({
    patient_id: {
        type: String,
        required: true
    },
    tumor_core: {
        type: String,
        required: true
    },
    whole_tumor: {
        type: String,
        required: true
    },
    enhancing_tumor: {
        type: String,
        required: true
    },
    volumes_mm3: {
        tumor_core: {
            type: String,
            required: false,
            default: null
        },
        whole_tumor: {
            type: String,
            required: false,
            default: null
        },
        enhancing_tumor: {
            type: String,
            required: false,
            default: null
        },
        brain_volume: {
            type: String,
            required: false,
            default: null
        }
    },
    confidence: {
        tumor_core: {
            type: Number,
            default: null
        },
        whole_tumor: {
            type: Number,
            default: null
        },
        enhancing_tumor: {
            type: Number,
            default: null
        }
    },
    stage: {
        brain_volume: {
            type: String,
            required: false,
            default: null
        }
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'user',
        required: true
    },
    mriUploadId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'mriUpload',
        required: false
    }
});

const fastapiAnalysisModel = mongoose.models.fastapiAnalysis || mongoose.model('fastapiAnalysis', fastapiAnalysisSchema);

export default fastapiAnalysisModel;
