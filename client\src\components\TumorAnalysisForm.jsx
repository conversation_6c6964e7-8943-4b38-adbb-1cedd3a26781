/* eslint-disable no-unused-vars */
import React, { useState, useContext } from 'react';
import axios from 'axios';
import { AppContent } from '../context/AppContext';
import { toast } from 'react-toastify';

const TumorAnalysisForm = () => {
    const { token, backendUrl } = useContext(AppContent);
    const [formData, setFormData] = useState({
        patient_id: '',
        image_path: '',
        file: null
    });
    const [analysisMode, setAnalysisMode] = useState('file'); // 'file' or 'path'
    const [loading, setLoading] = useState(false);
    const [results, setResults] = useState(null);
    const [fastapiMode, setFastapiMode] = useState('backend'); // 'direct' or 'backend'

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleFileChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            // Validate file type
            if (!file.name.match(/\.(nii|nii\.gz)$/)) {
                toast.error('Please select a .nii or .nii.gz file');
                return;
            }
            setFormData(prev => ({
                ...prev,
                file: file
            }));
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setResults(null);

        try {
            const submitData = new FormData();
            submitData.append('patient_id', formData.patient_id);

            if (analysisMode === 'file' && formData.file) {
                submitData.append('nifti_file', formData.file);
            } else if (analysisMode === 'path' && formData.image_path) {
                submitData.append('image_path', formData.image_path);
            } else {
                toast.error('Please provide either a file or file path');
                setLoading(false);
                return;
            }

            let response;

            if (fastapiMode === 'direct') {
                // Direct FastAPI call
                response = await axios.post(
                    'http://localhost:8000/analyze',
                    submitData,
                    {
                        headers: {
                            'Content-Type': 'multipart/form-data'
                        },
                        timeout: 300000 // 5 minutes
                    }
                );
            } else {
                // Through backend integration
                response = await axios.post(
                    `${backendUrl}/api/fastapi/analyze`,
                    submitData,
                    {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'multipart/form-data'
                        },
                        timeout: 300000 // 5 minutes
                    }
                );
            }

            if (response.data.success) {
                setResults(response.data.results || response.data);
                toast.success('Tumor analysis completed successfully!');
            } else {
                toast.error(response.data.message || 'Analysis failed');
            }

        } catch (error) {
            console.error('Analysis error:', error);

            if (error.code === 'ECONNABORTED') {
                toast.error('Analysis timeout. Please try again.');
            } else if (error.response) {
                toast.error(error.response.data.message || error.response.data.detail || 'Analysis failed');
            } else {
                toast.error('Network error. Please check your connection.');
            }
        } finally {
            setLoading(false);
        }
    };

    const getStageColor = (stage) => {
        switch (stage) {
            case 'Stage I': return 'text-green-600 bg-green-100';
            case 'Stage II': return 'text-yellow-600 bg-yellow-100';
            case 'Stage III': return 'text-orange-600 bg-orange-100';
            case 'Stage IV': return 'text-red-600 bg-red-100';
            default: return 'text-gray-600 bg-gray-100';
        }
    };

    const getConfidenceColor = (confidence) => {
        if (confidence >= 0.8) return 'text-green-600';
        if (confidence >= 0.6) return 'text-yellow-600';
        return 'text-red-600';
    };

    return (
        <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg space-y-6">
            {/* Header */}
            <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    FastAPI Tumor Analysis
                </h2>
                <p className="text-gray-600">
                    Advanced AI-powered brain tumor segmentation using MONAI UNet
                </p>
            </div>

            {/* FastAPI Integration Mode Selection */}
            <div className="flex justify-center space-x-4 mb-4">
                <button
                    type="button"
                    onClick={() => setFastapiMode('backend')}
                    className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                        fastapiMode === 'backend'
                            ? 'bg-green-600 text-white shadow-lg'
                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                >
                    🔗 Backend Integration
                </button>
                <button
                    type="button"
                    onClick={() => setFastapiMode('direct')}
                    className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                        fastapiMode === 'direct'
                            ? 'bg-purple-600 text-white shadow-lg'
                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                >
                    ⚡ Direct FastAPI
                </button>
            </div>

            {/* Analysis Mode Selection */}
            <div className="flex justify-center space-x-4 mb-6">
                <button
                    type="button"
                    onClick={() => setAnalysisMode('file')}
                    className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                        analysisMode === 'file'
                            ? 'bg-blue-600 text-white shadow-lg'
                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                >
                    Upload File
                </button>
                <button
                    type="button"
                    onClick={() => setAnalysisMode('path')}
                    className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                        analysisMode === 'path'
                            ? 'bg-blue-600 text-white shadow-lg'
                            : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                >
                    File Path
                </button>
            </div>

            {/* Integration Mode Info */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                    <span className="text-lg">
                        {fastapiMode === 'backend' ? '🔗' : '⚡'}
                    </span>
                    <h3 className="text-sm font-medium text-gray-900">
                        {fastapiMode === 'backend' ? 'Backend Integration Mode' : 'Direct FastAPI Mode'}
                    </h3>
                </div>
                <p className="text-sm text-gray-600">
                    {fastapiMode === 'backend'
                        ? 'Analysis requests go through Express.js backend with authentication, error handling, and MongoDB storage.'
                        : 'Direct communication with FastAPI server (main.py) for faster processing without backend overhead.'
                    }
                </p>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className="space-y-4">
                {/* Patient ID */}
                <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                        Patient ID
                    </label>
                    <input
                        type="text"
                        name="patient_id"
                        value={formData.patient_id}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="e.g., BRATS_876"
                        required
                    />
                </div>

                {/* File Upload Mode */}
                {analysisMode === 'file' && (
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            NIfTI File (.nii or .nii.gz)
                        </label>
                        <input
                            type="file"
                            accept=".nii,.nii.gz"
                            onChange={handleFileChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                            required
                        />
                        {formData.file && (
                            <p className="text-sm text-gray-600 mt-1">
                                Selected: {formData.file.name} ({(formData.file.size / (1024 * 1024)).toFixed(2)} MB)
                            </p>
                        )}
                    </div>
                )}

                {/* File Path Mode */}
                {analysisMode === 'path' && (
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Image Path (FLAIR modality _0000.nii.gz)
                        </label>
                        <input
                            type="text"
                            name="image_path"
                            value={formData.image_path}
                            onChange={handleInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="e.g., D:/mern-auth/api/dataset/imagesTs/BRATS_485_0000.nii.gz"
                            required
                        />
                    </div>
                )}

                {/* Submit Button */}
                <button
                    type="submit"
                    disabled={loading}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-purple-600 hover:to-blue-600 text-white font-semibold py-3 px-6 rounded-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                    {loading ? (
                        <div className="flex items-center justify-center space-x-2">
                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                            <span>Analyzing...</span>
                        </div>
                    ) : (
                        'Analyze Tumor'
                    )}
                </button>
            </form>

            {/* Results Display */}
            {results && (
                <div className="mt-8 space-y-6 animate-fade-in">
                    <h3 className="text-xl font-bold text-gray-900 text-center">
                        Analysis Results
                    </h3>

                    {/* Summary Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {[
                            { key: 'tumor_core', label: 'Tumor Core', icon: '🎯', color: 'blue' },
                            { key: 'whole_tumor', label: 'Whole Tumor', icon: '🧠', color: 'green' },
                            { key: 'enhancing_tumor', label: 'Enhancing Tumor', icon: '⚡', color: 'yellow' }
                        ].map((item) => (
                            <div key={item.key} className="bg-white p-4 rounded-lg shadow-md border border-gray-200">
                                <div className="flex items-center justify-between mb-3">
                                    <div className="flex items-center space-x-2">
                                        <span className="text-2xl">{item.icon}</span>
                                        <h4 className="font-semibold text-gray-900">{item.label}</h4>
                                    </div>
                                </div>
                                
                                <div className="space-y-2">
                                    <div className="flex justify-between">
                                        <span className="text-sm text-gray-600">Percentage:</span>
                                        <span className="font-bold text-lg">{results[item.key]}</span>
                                    </div>
                                    
                                    {results.volumes_mm3 && (
                                        <div className="flex justify-between">
                                            <span className="text-sm text-gray-600">Volume:</span>
                                            <span className="font-medium">{results.volumes_mm3[item.key]}</span>
                                        </div>
                                    )}
                                    
                                    {results.confidence && results.confidence[item.key] !== null && (
                                        <div className="flex justify-between">
                                            <span className="text-sm text-gray-600">Confidence:</span>
                                            <span className={`font-medium ${getConfidenceColor(results.confidence[item.key])}`}>
                                                {(results.confidence[item.key] * 100).toFixed(1)}%
                                            </span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>

                    {/* Stage and Brain Volume */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {results.stage && (
                            <div className="bg-white p-4 rounded-lg shadow-md border border-gray-200">
                                <h4 className="font-semibold text-gray-900 mb-3">Tumor Stage</h4>
                                <div className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getStageColor(results.stage.brain_volume || results.stage)}`}>
                                    {results.stage.brain_volume || results.stage}
                                </div>
                            </div>
                        )}
                        
                        {results.volumes_mm3 && results.volumes_mm3.brain_volume && (
                            <div className="bg-white p-4 rounded-lg shadow-md border border-gray-200">
                                <h4 className="font-semibold text-gray-900 mb-3">Brain Volume</h4>
                                <p className="text-lg font-bold text-gray-900">
                                    {results.volumes_mm3.brain_volume}
                                </p>
                            </div>
                        )}
                    </div>

                    {/* Additional Information */}
                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                        <h4 className="font-semibold text-blue-900 mb-2">Patient Information</h4>
                        <p className="text-blue-800">Patient ID: {formData.patient_id}</p>
                        {formData.file && (
                            <p className="text-blue-800">Analyzed File: {formData.file.name}</p>
                        )}
                        {formData.image_path && analysisMode === 'path' && (
                            <p className="text-blue-800">Image Path: {formData.image_path}</p>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};

export default TumorAnalysisForm;