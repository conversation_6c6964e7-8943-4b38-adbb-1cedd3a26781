from flask import Flask, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

@app.route('/health', methods=['GET'])
def health():
    return jsonify({"status": "ok", "message": "Flask server is running"})

@app.route('/analyze', methods=['POST'])
def analyze():
    return jsonify({
        "results": {
            "confidence": {
                "enhancing_tumor": 0.8749,
                "tumor_core": 0.9314,
                "whole_tumor": 0.9554
            },
            "enhancing_tumor": "2.81%",
            "stage": {
                "brain_volume": "Stage III"
            },
            "tumor_core": "14.43%",
            "volumes_mm3": {
                "brain_volume": "1,261,612mm³",
                "enhancing_tumor": "35,475mm³",
                "tumor_core": "182,070mm³",
                "whole_tumor": "172,641mm³"
            },
            "whole_tumor": "13.68%"
        }
    })

if __name__ == '__main__':
    print("Starting Flask server on port 8000...")
    app.run(debug=True, host='127.0.0.1', port=8000)
