from flask import Flask, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

@app.route('/health', methods=['GET'])
def health():
    return jsonify({"status": "ok", "message": "Flask server is running"})

@app.route('/analyze', methods=['POST'])
def analyze():
    return jsonify({
        "patient_id": "TEST_001",
        "tumor_core": {"percentage": 15.7, "volume_ml": 12.3, "confidence": 0.89},
        "whole_tumor": {"percentage": 28.4, "volume_ml": 22.1, "confidence": 0.92},
        "enhancing_tumor": {"percentage": 8.2, "volume_ml": 6.4, "confidence": 0.85},
        "stage": "Grade II",
        "confidence_score": 0.88
    })

if __name__ == '__main__':
    print("Starting Flask server on port 8000...")
    app.run(debug=True, host='127.0.0.1', port=8000)
