from flask import Flask, request, jsonify
from flask_cors import CORS
import json

app = Flask(__name__)
CORS(app, 
     origins=["http://localhost:5173"],  
     supports_credentials=True,
     allow_headers=['Content-Type', 'Authorization'],
     methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])

@app.route('/analyze', methods=['POST'])
def analyze():
    try:
        print("📥 Received analysis request")
        
        # Get patient_id from form data
        patient_id = request.form.get('patient_id', 'UNKNOWN')
        print(f"👤 Patient ID: {patient_id}")
        
        # Check if file was uploaded
        if 'nifti_file' in request.files:
            file = request.files['nifti_file']
            print(f"📁 File received: {file.filename}")
            print(f"📊 File size: {len(file.read())} bytes")
            file.seek(0)  # Reset file pointer
        else:
            print("❌ No file received")
            return jsonify({"error": "No file uploaded"}), 400
        
        # Return mock analysis results
        mock_results = {
            "patient_id": patient_id,
            "tumor_core": {
                "percentage": 15.7,
                "volume_ml": 12.3,
                "confidence": 0.89
            },
            "whole_tumor": {
                "percentage": 28.4,
                "volume_ml": 22.1,
                "confidence": 0.92
            },
            "enhancing_tumor": {
                "percentage": 8.2,
                "volume_ml": 6.4,
                "confidence": 0.85
            },
            "stage": "Grade II",
            "confidence_score": 0.88,
            "analysis_timestamp": "2024-12-30T10:30:00Z",
            "model_version": "UNet_v1.0_test"
        }
        
        print("✅ Returning mock analysis results")
        return jsonify(mock_results)
        
    except Exception as e:
        print(f"❌ Error in analysis: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/health', methods=['GET'])
def health():
    return jsonify({"status": "healthy", "message": "Flask API is running"})

if __name__ == '__main__':
    print("🚀 Starting Simple Flask Test Server on http://localhost:8000")
    print("🌐 CORS enabled for http://localhost:5173")
    print("📋 Available endpoints:")
    print("   - POST /analyze (for tumor analysis)")
    print("   - GET /health (for health check)")
    app.run(debug=True, host='0.0.0.0', port=8000)
