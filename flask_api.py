from flask import Flask, jsonify, request
from flask_cors import CORS

app = Flask(__name__)

# Configure CORS properly
CORS(app, 
     origins=["http://localhost:5173", "http://127.0.0.1:5173"],  
     supports_credentials=True,
     allow_headers=['Content-Type', 'Authorization'],
     methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])

@app.route('/', methods=['GET'])
def home():
    return jsonify({"message": "Flask API - CORS enabled for MRI Analysis"})

@app.route('/analyze', methods=['POST', 'OPTIONS'])
def analyze():
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = jsonify({'status': 'OK'})
        response.headers.add('Access-Control-Allow-Origin', 'http://localhost:5173')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
        return response
    
    try:
        # Check if it's a file upload
        if 'nifti_file' in request.files:
            # File upload
            patient_id = request.form.get('patient_id', 'UNKNOWN')
            file = request.files['nifti_file']
            
            print(f"✅ Received file upload: {file.filename}, Patient ID: {patient_id}")
            
            # Mock analysis results (replace with actual ML model)
            results = {
                "tumor_core": "2.34%",
                "whole_tumor": "5.67%", 
                "enhancing_tumor": "1.23%",
                "volumes_mm3": {
                    "tumor_core": "1234.56 mm³",
                    "whole_tumor": "2345.67 mm³",
                    "enhancing_tumor": "567.89 mm³",
                    "brain_volume": "1500000.00 mm³"
                },
                "confidence": {
                    "tumor_core": 0.95,
                    "whole_tumor": 0.92,
                    "enhancing_tumor": 0.88
                },
                "stage": {
                    "brain_volume": "Stage II"
                }
            }
            
            response = jsonify({
                "success": True,
                "patient_id": patient_id,
                "filename": file.filename,
                "results": results
            })
            response.headers.add('Access-Control-Allow-Origin', 'http://localhost:5173')
            return response
        else:
            return jsonify({"error": "No file uploaded"}), 400
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        response = jsonify({"error": str(e)})
        response.headers.add('Access-Control-Allow-Origin', 'http://localhost:5173')
        return response, 400

if __name__ == '__main__':
    print("🚀 Starting Flask API server on http://localhost:8001")
    print("🌐 CORS enabled for http://localhost:5173")
    print("📁 Ready to receive MRI file uploads at /analyze endpoint")
    app.run(debug=True, host='127.0.0.1', port=8001)
