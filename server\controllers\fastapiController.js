import axios from 'axios';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import fastapiAnalysisModel from '../models/fastapiAnalysisModel.js';
import mriUploadModel from '../models/mriUploadModel.js';

const FASTAPI_URL = process.env.FASTAPI_URL || 'http://127.0.0.1:8000';

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = 'uploads/nifti';
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        // Keep original filename for NIfTI files
        cb(null, file.originalname);
    }
});

const fileFilter = (req, file, cb) => {
    // Accept only .nii and .nii.gz files
    if (file.originalname.match(/\.(nii|nii\.gz)$/)) {
        cb(null, true);
    } else {
        cb(new Error('Only .nii and .nii.gz files are allowed!'), false);
    }
};

export const upload = multer({ 
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 500 * 1024 * 1024 // 500MB limit for NIfTI files
    }
});

// Analyze tumor using FastAPI
export const analyzeTumorFastAPI = async (req, res) => {
    try {
        const { patient_id, image_path } = req.body;
        const uploadedFile = req.file;

        let analysisPayload;
        let fastapiEndpoint;

        if (uploadedFile) {
            // File upload scenario - use /upload-and-analyze endpoint
            fastapiEndpoint = `${FASTAPI_URL}/upload-and-analyze`;
            
            // Create FormData for file upload
            const FormData = (await import('form-data')).default;
            const formData = new FormData();
            formData.append('patient_id', patient_id);
            formData.append('file', fs.createReadStream(uploadedFile.path), {
                filename: uploadedFile.originalname,
                contentType: 'application/octet-stream'
            });

            analysisPayload = formData;
        } else if (image_path) {
            // File path scenario - use /analyze endpoint
            fastapiEndpoint = `${FASTAPI_URL}/analyze`;
            analysisPayload = {
                patient_id: patient_id,
                image: image_path
            };
        } else {
            return res.status(400).json({
                success: false,
                message: 'Either upload a file or provide an image path'
            });
        }

        console.log(`Calling FastAPI endpoint: ${fastapiEndpoint}`);
        console.log(`Patient ID: ${patient_id}`);

        // Call FastAPI
        const fastapiResponse = await axios.post(fastapiEndpoint, analysisPayload, {
            headers: uploadedFile ? analysisPayload.getHeaders() : { 'Content-Type': 'application/json' },
            timeout: 300000 // 5 minutes timeout
        });

        const results = fastapiResponse.data.results;

        // Save results to MongoDB
        const analysisRecord = new fastapiAnalysisModel({
            patient_id: patient_id,
            tumor_core: results.tumor_core,
            whole_tumor: results.whole_tumor,
            enhancing_tumor: results.enhancing_tumor,
            volumes_mm3: results.volumes_mm3,
            confidence: results.confidence,
            stage: results.stage,
            userId: req.user._id,
            mriUploadId: uploadedFile ? null : null // Could link to existing upload if needed
        });

        await analysisRecord.save();

        // Clean up uploaded file if it was temporary
        if (uploadedFile && fs.existsSync(uploadedFile.path)) {
            fs.unlinkSync(uploadedFile.path);
        }

        res.json({
            success: true,
            message: 'Tumor analysis completed successfully',
            results: results,
            analysisId: analysisRecord._id
        });

    } catch (error) {
        console.error('FastAPI Analysis Error:', error);
        
        // Clean up uploaded file on error
        if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }

        if (error.code === 'ECONNREFUSED') {
            return res.status(503).json({
                success: false,
                message: 'FastAPI server is unavailable. Please try again later.'
            });
        }

        if (error.response) {
            return res.status(error.response.status).json({
                success: false,
                message: error.response.data.detail || 'FastAPI analysis failed',
                error: error.response.data
            });
        }

        res.status(500).json({
            success: false,
            message: 'Internal server error during analysis',
            error: error.message
        });
    }
};

// Get FastAPI analysis history
export const getFastapiAnalysisHistory = async (req, res) => {
    try {
        const analyses = await fastapiAnalysisModel.find({ userId: req.user._id })
            .sort({ createdAt: -1 })
            .limit(50);

        res.json({
            success: true,
            analyses: analyses
        });

    } catch (error) {
        console.error('Error fetching analysis history:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching analysis history'
        });
    }
};

// Get specific FastAPI analysis
export const getFastapiAnalysis = async (req, res) => {
    try {
        const { analysisId } = req.params;

        const analysis = await fastapiAnalysisModel.findOne({
            _id: analysisId,
            userId: req.user._id
        });

        if (!analysis) {
            return res.status(404).json({
                success: false,
                message: 'Analysis not found'
            });
        }

        res.json({
            success: true,
            analysis: analysis
        });

    } catch (error) {
        console.error('Error fetching analysis:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching analysis'
        });
    }
};

// Save Flask API analysis results from frontend
export const saveFlaskAnalysis = async (req, res) => {
    try {
        const {
            patient_id,
            tumor_core,
            whole_tumor,
            enhancing_tumor,
            volumes_mm3,
            confidence,
            stage,
            filename
        } = req.body;

        // Validate required fields
        if (!patient_id || !tumor_core || !whole_tumor || !enhancing_tumor) {
            return res.status(400).json({
                success: false,
                message: 'Missing required analysis data'
            });
        }

        // Save results to MongoDB
        const analysisRecord = new fastapiAnalysisModel({
            patient_id: patient_id,
            tumor_core: tumor_core,
            whole_tumor: whole_tumor,
            enhancing_tumor: enhancing_tumor,
            volumes_mm3: volumes_mm3 || {},
            confidence: confidence || {},
            stage: stage || {},
            userId: req.user._id,
            mriUploadId: null // Could be linked if needed
        });

        await analysisRecord.save();

        res.json({
            success: true,
            message: 'Flask API analysis results saved successfully',
            analysisId: analysisRecord._id,
            analysis: analysisRecord
        });

    } catch (error) {
        console.error('Error saving Flask analysis:', error);
        res.status(500).json({
            success: false,
            message: 'Error saving analysis results',
            error: error.message
        });
    }
};
