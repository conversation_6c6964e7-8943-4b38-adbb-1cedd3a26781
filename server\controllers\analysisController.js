import analysisModel from '../models/analysisModel.js';
import mriUploadModel from '../models/mriUploadModel.js';

// Generate dummy analysis results
const generateDummyAnalysis = () => {
    const tumorDetected = Math.random() > 0.3; // 70% chance of tumor detection
    
    if (!tumorDetected) {
        return {
            confidenceScore: Math.floor(Math.random() * 20) + 80,
            processingTime: Math.floor(Math.random() * 30) + 10,
            additionalFindings: "No significant abnormalities detected.",
            recommendations: "Continue regular monitoring. Follow up in 6 months."
        };
    }

    
    const tumorGrades = ['Stage I', 'Stage II', 'Stage III', 'Stage IV'];
   

    const level = tumorLevels[Math.floor(Math.random() * tumorLevels.length)];
    const grade = tumorGrades[Math.floor(Math.random() * tumorGrades.length)];
    

    return {
        
        tumorLevel: level,
        tumorGrade: grade,
        tumorVolume: Math.floor(Math.random() * 15000) + 1000,
        tumorLocation: {
            x: Math.floor(Math.random() * 256),
            y: Math.floor(Math.random() * 256),
            z: Math.floor(Math.random() * 180)
        },
        confidenceScore: Math.floor(Math.random() * 25) + 75,
        processingTime: Math.floor(Math.random() * 120) + 30,
        additionalFindings: level === 'Critical' ? 
            "Mass effect and midline shift observed. Urgent intervention required." :
            "Well-defined lesion with clear boundaries. No immediate complications.",
        recommendations: level === 'Critical' ? 
            "Immediate neurosurgical consultation required. Consider emergency intervention." :
            level === 'High' ?
            "Urgent oncological consultation. Biopsy recommended within 48 hours." :
            "Schedule follow-up with oncologist. Consider treatment options."
    };
};

// Analyze uploaded MRI
export const analyzeMRI = async (req, res) => {
    try {
        const { uploadId } = req.params;

        const upload = await mriUploadModel.findOne({
            _id: uploadId,
            userId: req.user._id
        });

        if (!upload) {
            return res.status(404).json({
                success: false,
                message: "Upload not found"
            });
        }

        if (upload.status === 'analyzed') {
            return res.status(400).json({
                success: false,
                message: "This MRI has already been analyzed"
            });
        }

        // Update upload status to processing
        upload.status = 'processing';
        await upload.save();

        // Simulate processing delay
        setTimeout(async () => {
            try {
                // Generate dummy analysis
                const analysisData = generateDummyAnalysis();

                const analysis = new analysisModel({
                    mriUploadId: uploadId,
                    userId: req.user._id,
                    ...analysisData
                });

                await analysis.save();

                // Update upload with analysis ID and status
                upload.analysisId = analysis._id;
                upload.status = 'analyzed';
                await upload.save();

            } catch (error) {
                console.log('Error in background analysis:', error);
                upload.status = 'error';
                await upload.save();
            }
        }, 2000); // 2 second delay to simulate processing

        res.json({
            success: true,
            message: "Analysis started. Please wait for results.",
            uploadId: uploadId
        });

    } catch (error) {
        console.log(error);
        res.status(500).json({
            success: false,
            message: "Error starting analysis"
        });
    }
};

// Get analysis results
export const getAnalysisResults = async (req, res) => {
    try {
        const { uploadId } = req.params;

        const upload = await mriUploadModel.findOne({
            _id: uploadId,
            userId: req.user._id
        }).populate('analysisId');

        if (!upload) {
            return res.status(404).json({
                success: false,
                message: "Upload not found"
            });
        }

        if (upload.status === 'processing') {
            return res.json({
                success: true,
                status: 'processing',
                message: "Analysis in progress..."
            });
        }

        if (upload.status === 'error') {
            return res.status(500).json({
                success: false,
                message: "Analysis failed. Please try again."
            });
        }

        if (!upload.analysisId) {
            return res.status(404).json({
                success: false,
                message: "Analysis not found"
            });
        }

        res.json({
            success: true,
            status: 'completed',
            analysis: upload.analysisId,
            upload: {
                fileName: upload.originalName,
                uploadDate: upload.uploadDate
            }
        });

    } catch (error) {
        console.log(error);
        res.status(500).json({
            success: false,
            message: "Error fetching analysis results"
        });
    }
};

// Get all analyses for admin
export const getAllAnalyses = async (req, res) => {
    try {
        const analyses = await analysisModel.find()
            .populate('userId', 'name email')
            .populate('mriUploadId', 'originalName uploadDate')
            .sort({ analysisDate: -1 });

        res.json({
            success: true,
            analyses
        });

    } catch (error) {
        console.log(error);
        res.status(500).json({
            success: false,
            message: "Error fetching analyses"
        });
    }
};
