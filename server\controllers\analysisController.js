import analysisModel from '../models/analysisModel.js';
import mriUploadModel from '../models/mriUploadModel.js';

// Generate dummy analysis results with new format
const generateDummyAnalysis = () => {
    
    const tumorGrades = ['Stage I', 'Stage II', 'Stage III', 'Stage IV'];   
    const grade = tumorGrades[Math.floor(Math.random() * tumorGrades.length)];
  

    // Generate random percentages
    const tumorCorePercent = (Math.random() * 20 + 5).toFixed(2);
    const wholeTumorPercent = (Math.random() * 25 + 10).toFixed(2);
    const enhancingTumorPercent = (Math.random() * 8 + 1).toFixed(2);

    // Generate random volumes
    const brainVolume = Math.floor(Math.random() * 500000 + 1200000);
    const tumorCoreVolume = Math.floor(Math.random() * 200000 + 50000);
    const wholeTumorVolume = Math.floor(Math.random() * 300000 + 100000);
    const enhancingTumorVolume = Math.floor(Math.random() * 50000 + 10000);

    return {
       
        
        processingTime: Math.floor(Math.random() * 120) + 30,
       
        // New format results
        results: {
            confidence: {
                enhancing_tumor: (Math.random() * 0.3 + 0.7).toFixed(4),
                tumor_core: (Math.random() * 0.3 + 0.7).toFixed(4),
                whole_tumor: (Math.random() * 0.3 + 0.7).toFixed(4)
            },
            enhancing_tumor: `${enhancingTumorPercent}%`,
            stage: {
                brain_volume: grade
            },
            tumor_core: `${tumorCorePercent}%`,
            volumes_mm3: {
                brain_volume: `${brainVolume.toLocaleString()}mm³`,
                enhancing_tumor: `${enhancingTumorVolume.toLocaleString()}mm³`,
                tumor_core: `${tumorCoreVolume.toLocaleString()}mm³`,
                whole_tumor: `${wholeTumorVolume.toLocaleString()}mm³`
            },
            whole_tumor: `${wholeTumorPercent}%`
        }
    };
};

// Analyze uploaded MRI
export const analyzeMRI = async (req, res) => {
    try {
        const { uploadId } = req.params;

        const upload = await mriUploadModel.findOne({
            _id: uploadId,
            userId: req.user._id
        });

        if (!upload) {
            return res.status(404).json({
                success: false,
                message: "Upload not found"
            });
        }

        if (upload.status === 'analyzed') {
            return res.status(400).json({
                success: false,
                message: "This MRI has already been analyzed"
            });
        }

        // Update upload status to processing
        upload.status = 'processing';
        await upload.save();

        // Simulate processing delay
        setTimeout(async () => {
            try {
                // Generate dummy analysis
                const analysisData = generateDummyAnalysis();

                const analysis = new analysisModel({
                    mriUploadId: uploadId,
                    userId: req.user._id,
                    ...analysisData
                });

                await analysis.save();

                // Update upload with analysis ID and status
                upload.analysisId = analysis._id;
                upload.status = 'analyzed';
                await upload.save();

            } catch (error) {
                console.log('Error in background analysis:', error);
                upload.status = 'error';
                await upload.save();
            }
        }, 2000); // 2 second delay to simulate processing

        res.json({
            success: true,
            message: "Analysis started. Please wait for results.",
            uploadId: uploadId
        });

    } catch (error) {
        console.log(error);
        res.status(500).json({
            success: false,
            message: "Error starting analysis"
        });
    }
};

// Get analysis results
export const getAnalysisResults = async (req, res) => {
    try {
        const { uploadId } = req.params;

        const upload = await mriUploadModel.findOne({
            _id: uploadId,
            userId: req.user._id
        }).populate('analysisId');

        if (!upload) {
            return res.status(404).json({
                success: false,
                message: "Upload not found"
            });
        }

        if (upload.status === 'processing') {
            return res.json({
                success: true,
                status: 'processing',
                message: "Analysis in progress..."
            });
        }

        if (upload.status === 'error') {
            return res.status(500).json({
                success: false,
                message: "Analysis failed. Please try again."
            });
        }

        if (!upload.analysisId) {
            return res.status(404).json({
                success: false,
                message: "Analysis not found"
            });
        }

        res.json({
            success: true,
            status: 'completed',
            analysis: upload.analysisId,
            results: upload.analysisId.results, // Include the new results format
            upload: {
                fileName: upload.originalName,
                uploadDate: upload.uploadDate
            }
        });

    } catch (error) {
        console.log(error);
        res.status(500).json({
            success: false,
            message: "Error fetching analysis results"
        });
    }
};

// Get all analyses for admin
export const getAllAnalyses = async (req, res) => {
    try {
        const analyses = await analysisModel.find()
            .populate('userId', 'name email')
            .populate('mriUploadId', 'originalName uploadDate')
            .sort({ analysisDate: -1 });

        res.json({
            success: true,
            analyses
        });

    } catch (error) {
        console.log(error);
        res.status(500).json({
            success: false,
            message: "Error fetching analyses"
        });
    }
};
