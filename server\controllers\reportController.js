import reportModel from '../models/reportModel.js';
import analysisModel from '../models/analysisModel.js';
import mriUploadModel from '../models/mriUploadModel.js';
import userModel from '../models/userModel.js';

// Generate report from analysis
export const generateReport = async (req, res) => {
    try {
        const { analysisId } = req.params;
        const { patientInfo, clinicalHistory } = req.body;

        const analysis = await analysisModel.findOne({
            _id: analysisId,
            userId: req.user._id
        }).populate('mriUploadId');

        if (!analysis) {
            return res.status(404).json({
                success: false,
                message: "Analysis not found"
            });
        }

        // Check if report already exists
        const existingReport = await reportModel.findOne({ analysisId });
        if (existingReport) {
            return res.status(400).json({
                success: false,
                message: "Report already exists for this analysis"
            });
        }

        // Generate report content based on analysis
        const findings = generateFindings(analysis);
      

        const report = new reportModel({
            analysisId: analysisId,
            userId: req.user._id,
            reportTitle: `Brain MRI Analysis Report - ${analysis.mriUploadId.originalName}`,
            patientInfo: patientInfo || {
                name: req.user.name,
                age: null,
                gender: null,
                patientId: `PAT-${Date.now()}`
            },
            clinicalHistory: clinicalHistory || "No clinical history provided",
            scanDetails: {
                scanType: "MRI Brain",
                scanDate: analysis.mriUploadId.uploadDate,
                contrast: false,
                technique: "T1-weighted, T2-weighted, FLAIR sequences"
            },
            findings,
            priority: determinePriority(analysis)
        });

        await report.save();

        // Update MRI upload with report ID
        await mriUploadModel.findByIdAndUpdate(analysis.mriUploadId._id, {
            reportId: report._id
        });

        res.json({
            success: true,
            message: "Report generated successfully",
            reportId: report._id
        });

    } catch (error) {
        console.log(error);
        res.status(500).json({
            success: false,
            message: "Error generating report"
        });
    }
};

// Get report details
export const getReport = async (req, res) => {
    try {
        const { reportId } = req.params;

        const report = await reportModel.findById(reportId)
            .populate('analysisId')
            .populate('userId', 'name email')
            .populate('assignedDoctorId', 'name email specialization');

        if (!report) {
            return res.status(404).json({
                success: false,
                message: "Report not found"
            });
        }

        // Check access permissions
        if (req.user.role === 'user' && report.userId._id.toString() !== req.user._id.toString()) {
            return res.status(403).json({
                success: false,
                message: "Access denied"
            });
        }

        if (req.user.role === 'doctor' &&
            (!report.assignedDoctorId || report.assignedDoctorId._id.toString() !== req.user._id.toString())) {
            return res.status(403).json({
                success: false,
                message: "This report is not assigned to you"
            });
        }

        // Admin can access all reports - no additional check needed

        res.json({
            success: true,
            report
        });

    } catch (error) {
        console.log(error);
        res.status(500).json({
            success: false,
            message: "Error fetching report"
        });
    }
};

// Assign report to doctor
export const assignReportToDoctor = async (req, res) => {
    try {
        const { reportId } = req.params;
        const { doctorId } = req.body;

        // Allow admin to assign any report, users can only assign their own reports
        let report;
        if (req.user.role === 'admin') {
            report = await reportModel.findById(reportId);
        } else {
            report = await reportModel.findOne({
                _id: reportId,
                userId: req.user._id
            });
        }

        if (!report) {
            return res.status(404).json({
                success: false,
                message: "Report not found"
            });
        }

        // Verify doctor exists and is approved
        const doctor = await userModel.findOne({
            _id: doctorId,
            role: 'doctor',
            isApproved: true
        });

        if (!doctor) {
            return res.status(404).json({
                success: false,
                message: "Doctor not found or not approved"
            });
        }

        report.assignedDoctorId = doctorId;
        report.status = 'assigned';
        await report.save();

        res.json({
            success: true,
            message: "Report assigned to doctor successfully"
        });

    } catch (error) {
        console.log(error);
        res.status(500).json({
            success: false,
            message: "Error assigning report"
        });
    }
};

// Doctor verify report
export const verifyReport = async (req, res) => {
    try {
        const { reportId } = req.params;
        const { doctorNotes, status } = req.body;

        const report = await reportModel.findOne({
            _id: reportId,
            assignedDoctorId: req.user._id
        });

        if (!report) {
            return res.status(404).json({
                success: false,
                message: "Report not found or not assigned to you"
            });
        }

        report.status = status; // 'verified' or 'rejected'
        report.doctorNotes = doctorNotes;
        report.verificationDate = new Date();
        await report.save();

        res.json({
            success: true,
            message: `Report ${status} successfully`
        });

    } catch (error) {
        console.log(error);
        res.status(500).json({
            success: false,
            message: "Error updating report"
        });
    }
};

// Get user's reports
export const getUserReports = async (req, res) => {
    try {
        let reports;

        // Admin can see all reports, users can only see their own
        if (req.user.role === 'admin') {
            reports = await reportModel.find({})
                .populate('userId', 'name email')
                .populate('assignedDoctorId', 'name specialization')
                .populate('analysisId', 'tumorGrade confidenceScore')
                .sort({ generatedDate: -1 });
        } else {
            reports = await reportModel.find({ userId: req.user._id })
                .populate('assignedDoctorId', 'name specialization')
                .populate('analysisId', 'tumorGrade confidenceScore')
                .sort({ generatedDate: -1 });
        }

        res.json({
            success: true,
            reports
        });

    } catch (error) {
        console.log(error);
        res.status(500).json({
            success: false,
            message: "Error fetching reports"
        });
    }
};

// Download report as PDF
export const downloadReportPDF = async (req, res) => {
    try {
        const { reportId } = req.params;

        const report = await reportModel.findById(reportId)
            .populate('analysisId')
            .populate('userId', 'name email')
            .populate('assignedDoctorId', 'name email specialization');

        if (!report) {
            return res.status(404).json({
                success: false,
                message: "Report not found"
            });
        }

        // Check access permissions
        if (req.user.role === 'user' && report.userId._id.toString() !== req.user._id.toString()) {
            return res.status(403).json({
                success: false,
                message: "Access denied"
            });
        }

        if (req.user.role === 'doctor' &&
            (!report.assignedDoctorId || report.assignedDoctorId._id.toString() !== req.user._id.toString())) {
            return res.status(403).json({
                success: false,
                message: "This report is not assigned to you"
            });
        }

        // Generate HTML content for the report
        const htmlContent = generateReportHTML(report);

        // Set headers for HTML download (temporary solution)
        const filename = `${report.reportTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_report.html`;
        res.setHeader('Content-Type', 'text/html');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

        // Send the HTML content
        res.send(htmlContent);

    } catch (error) {
        console.log(error);
        res.status(500).json({
            success: false,
            message: "Error generating report"
        });
    }
};

// Helper function to generate HTML report content
const generateReportHTML = (report) => {
    const formatDate = (date) => {
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const formatStatus = (status) => {
        return status.split('_').map(word =>
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
    };

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${report.reportTitle}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header h1 {
            color: #2563eb;
            margin: 0;
            font-size: 28px;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .section {
            background: white;
            margin: 20px 0;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section h2 {
            color: #2563eb;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
            margin-top: 0;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .info-item {
            padding: 15px;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid #2563eb;
        }
        .info-label {
            font-weight: bold;
            color: #374151;
            display: block;
            margin-bottom: 5px;
        }
        .info-value {
            color: #6b7280;
        }
        .status {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 12px;
        }
        .status.verified {
            background: #dcfce7;
            color: #166534;
        }
        .status.rejected {
            background: #fef2f2;
            color: #dc2626;
        }
        .status.generated {
            background: #dbeafe;
            color: #1d4ed8;
        }
        .status.assigned {
            background: #fef3c7;
            color: #d97706;
        }
        .status.under_review {
            background: #f3e8ff;
            color: #7c3aed;
        }
        .findings, .impression, .recommendations, .doctor-notes {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #10b981;
            margin: 15px 0;
        }
        .doctor-notes {
            border-left-color: #f59e0b;
        }
        .analysis-results {
            background: #eff6ff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #bfdbfe;
        }
        .tumor-detected {
            color: #dc2626;
            font-weight: bold;
        }
        .no-tumor {
            color: #059669;
            font-weight: bold;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            color: #6b7280;
            font-size: 14px;
        }
        @media print {
            body { background: white; }
            .section, .header, .footer { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 ${report.reportTitle}</h1>
        <p><strong>Generated:</strong> ${formatDate(report.generatedDate)}</p>
        <p><strong>Status:</strong> <span class="status ${report.status}">${formatStatus(report.status)}</span></p>
        <p><strong>Priority:</strong> ${report.priority || 'Standard'}</p>
    </div>

    <div class="section">
        <h2>👤 Patient Information</h2>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">Patient Name:</span>
                <span class="info-value">${report.patientInfo?.name || report.userId.name}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Email:</span>
                <span class="info-value">${report.userId.email}</span>
            </div>
            ${report.patientInfo?.age ? `
            <div class="info-item">
                <span class="info-label">Age:</span>
                <span class="info-value">${report.patientInfo.age} years</span>
            </div>
            ` : ''}
            ${report.patientInfo?.gender ? `
            <div class="info-item">
                <span class="info-label">Gender:</span>
                <span class="info-value">${report.patientInfo.gender}</span>
            </div>
            ` : ''}
        </div>
    </div>

    <div class="section">
        <h2>🔬 Scan Details</h2>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">Scan Type:</span>
                <span class="info-value">${report.scanDetails?.scanType || 'MRI Brain'}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Scan Date:</span>
                <span class="info-value">${formatDate(report.scanDetails?.scanDate || report.generatedDate)}</span>
            </div>
        </div>
    </div>

    ${report.analysisId ? `
    <div class="section">
        <h2>🤖 Analysis Results</h2>
        <div class="analysis-results">
            ${report.analysisId.results ? `
            <h3 style="color: #2563eb; margin-bottom: 15px;">📊 Tumor Analysis</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">Tumor Core:</span>
                    <span class="info-value">${report.analysisId.results.tumor_core}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Whole Tumor:</span>
                    <span class="info-value">${report.analysisId.results.whole_tumor}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Enhancing Tumor:</span>
                    <span class="info-value">${report.analysisId.results.enhancing_tumor}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Stage:</span>
                    <span class="info-value">${report.analysisId.results.stage.brain_volume}</span>
                </div>
            </div>

            <h3 style="color: #2563eb; margin: 20px 0 15px 0;">🧠 Volume Analysis</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">Brain Volume:</span>
                    <span class="info-value">${report.analysisId.results.volumes_mm3.brain_volume}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Tumor Core Volume:</span>
                    <span class="info-value">${report.analysisId.results.volumes_mm3.tumor_core}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Whole Tumor Volume:</span>
                    <span class="info-value">${report.analysisId.results.volumes_mm3.whole_tumor}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Enhancing Tumor Volume:</span>
                    <span class="info-value">${report.analysisId.results.volumes_mm3.enhancing_tumor}</span>
                </div>
            </div>

            <h3 style="color: #2563eb; margin: 20px 0 15px 0;">🎯 Confidence Scores</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">Tumor Core Confidence:</span>
                    <span class="info-value">${(parseFloat(report.analysisId.results.confidence.tumor_core) * 100).toFixed(1)}%</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Whole Tumor Confidence:</span>
                    <span class="info-value">${(parseFloat(report.analysisId.results.confidence.whole_tumor) * 100).toFixed(1)}%</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Enhancing Tumor Confidence:</span>
                    <span class="info-value">${(parseFloat(report.analysisId.results.confidence.enhancing_tumor) * 100).toFixed(1)}%</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Overall Confidence:</span>
                    <span class="info-value">${report.analysisId.confidenceScore}%</span>
                </div>
            </div>
            ` : `
           
            `}
        </div>
    </div>
    ` : ''}

    

   

    

    ${report.assignedDoctorId ? `
    <div class="section">
        <h2>👨‍⚕️ Assigned Doctor</h2>
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">Doctor Name:</span>
                <span class="info-value">Dr. ${report.assignedDoctorId.name}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Specialization:</span>
                <span class="info-value">${report.assignedDoctorId.specialization || 'General Medicine'}</span>
            </div>
        </div>
    </div>
    ` : ''}

    ${report.doctorNotes ? `
    <div class="section">
        <h2>📝 Doctor's Notes</h2>
        <div class="doctor-notes">
            ${report.doctorNotes}
        </div>
        ${report.verificationDate ? `
        <p style="margin-top: 15px; font-size: 14px; color: #6b7280;">
            <strong>Verified on:</strong> ${formatDate(report.verificationDate)}
        </p>
        ` : ''}
    </div>
    ` : ''}

    <div class="footer">
        <p><strong>Brain Tumor Detection System</strong></p>
        <p>This report was generated automatically by our AI-powered analysis system.</p>
        <p>For medical decisions, please consult with qualified healthcare professionals.</p>
        <p>Report ID: ${report._id}</p>
    </div>
</body>
</html>
    `;
};

// Get doctor's assigned reports
export const getDoctorReports = async (req, res) => {
    try {
        const reports = await reportModel.find({ assignedDoctorId: req.user._id })
            .populate('userId', 'name email')
            .populate('analysisId')
            .sort({ generatedDate: -1 });

        res.json({
            success: true,
            reports
        });

    } catch (error) {
        console.log(error);
        res.status(500).json({
            success: false,
            message: "Error fetching assigned reports"
        });
    }
};

// Helper functions
const generateFindings = (analysis) => {
    if (!analysis.tumorDetected) {
        return "The brain MRI examination shows normal brain parenchyma with no evidence of mass lesions, hemorrhage, or abnormal enhancement. Ventricular system appears normal in size and configuration. No midline shift is observed.";
    }

    if (analysis.results) {
        return `Brain MRI analysis reveals tumor involvement with the following characteristics:

        • Tumor Core: ${analysis.results.tumor_core} of brain volume
        • Whole Tumor: ${analysis.results.whole_tumor} of brain volume
        • Enhancing Tumor: ${analysis.results.enhancing_tumor} of brain volume
        • Stage Classification: ${analysis.results.stage.brain_volume}

        Volume measurements show:
        • Total Brain Volume: ${analysis.results.volumes_mm3.brain_volume}
        • Tumor Core Volume: ${analysis.results.volumes_mm3.tumor_core}
        • Whole Tumor Volume: ${analysis.results.volumes_mm3.whole_tumor}
        • Enhancing Tumor Volume: ${analysis.results.volumes_mm3.enhancing_tumor}

        Analysis confidence scores: Tumor Core (${(parseFloat(analysis.results.confidence.tumor_core) * 100).toFixed(1)}%), Whole Tumor (${(parseFloat(analysis.results.confidence.whole_tumor) * 100).toFixed(1)}%), Enhancing Tumor (${(parseFloat(analysis.results.confidence.enhancing_tumor) * 100).toFixed(1)}%).

        ${analysis.additionalFindings}`;
    }

    return `Brain MRI reveals a ${analysis.tumorType.toLowerCase()} measuring approximately ${Math.round(analysis.tumorVolume/1000)} cm³ in volume, located at coordinates (${analysis.tumorLocation.x}, ${analysis.tumorLocation.y}, ${analysis.tumorLocation.z}). The lesion demonstrates characteristics consistent with ${analysis.tumorGrade}. Total voxel count: ${analysis.numberOfVoxels.toLocaleString()}. ${analysis.additionalFindings}`;
};



const generateRecommendations = (analysis) => {
    return analysis.recommendations;
};

const determinePriority = (analysis) => {
    if (!analysis.tumorDetected) return 'Low';
    
    switch (analysis.tumorLevel) {
        case 'Critical': return 'Urgent';
        case 'High': return 'High';
        case 'Medium': return 'Medium';
        default: return 'Low';
    }
};
