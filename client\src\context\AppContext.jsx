/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react-refresh/only-export-components */
/* eslint-disable react/prop-types */


import { createContext, useEffect, useState } from "react";
import axios from 'axios';
import { toast } from "react-toastify";
import tokenManager from '../utils/tokenManager';



export const AppContent = createContext();

export const AppContextProvider = (props)=>{

    axios.defaults.withCredentials = true;

    const backendUrl = import.meta.env.VITE_BACKEND_URL
    const apiUrl = import.meta.env.API_URL
    const [isLoggedin, setIsLoggedin] = useState(false);
    const [userData, setUserData] = useState(false);
    const [loading, setLoading] = useState(true);

    // Set up axios interceptor to include token in all requests
    useEffect(() => {
        const requestInterceptor = axios.interceptors.request.use(
            (config) => {
                const token = tokenManager.getToken();
                if (token) {
                    config.headers.Authorization = `Bearer ${token}`;
                }
                return config;
            },
            (error) => {
                return Promise.reject(error);
            }
        );

        // Response interceptor to handle token expiration
        const responseInterceptor = axios.interceptors.response.use(
            (response) => response,
            (error) => {
                if (error.response?.status === 401) {
                    // Token expired or invalid
                    tokenManager.clearAuth();
                    setIsLoggedin(false);
                    setUserData(false);
                }
                return Promise.reject(error);
            }
        );

        return () => {
            axios.interceptors.request.eject(requestInterceptor);
            axios.interceptors.response.eject(responseInterceptor);
        };
    }, []);

    const getAuthState = async()=>{
        try {
            setLoading(true);

            // Check if token exists in localStorage first
            const token = tokenManager.getToken();
            const storedUserData = tokenManager.getUserData();

            if (!token || !storedUserData) {
                // No token or user data in localStorage
                setIsLoggedin(false);
                setUserData(false);
                setLoading(false);
                return;
            }

            // Verify token with backend
            const {data} = await axios.get(backendUrl+ '/api/auth/is-authenticated')

            if(data.success){
                setIsLoggedin(true);
                setUserData(data.user);
                // Update stored user data
                tokenManager.setUserData(data.user);
            } else {
                // Token invalid, clear localStorage
                tokenManager.clearAuth();
                setIsLoggedin(false);
                setUserData(false);
            }

        } catch (error) {
            // Network error or server error - don't show toast on page load
            console.log('Auth check failed:', error.message);

            // If it's a 401 error, clear auth data
            if (error.response?.status === 401) {
                tokenManager.clearAuth();
            }

            setIsLoggedin(false);
            setUserData(false);

            // Only show error toast if it's not a network error during initial load
            if (error.response && error.response.status !== 401) {
                toast.error('Authentication check failed');
            }
        } finally {
            setLoading(false);
        }
    }

    const getUserData = async ()=>{
        try {
            const {data} = await axios.get(backendUrl + '/api/user/data')
            data.success ? setUserData(data.userData) : toast.error(data.message)

        } catch (error) {
            toast.error(error.message)

        }
    }

    const logout = async () => {
        try {
            // Clear localStorage first
            tokenManager.clearAuth();
            setIsLoggedin(false);
            setUserData(false);

            // Then call backend to clear cookie (optional)
            await axios.post(backendUrl + '/api/auth/logout');
            toast.success("Logged out successfully");
        } catch (error) {
            // Even if backend call fails, we've cleared localStorage
            console.log('Logout error:', error.message);
            toast.success("Logged out successfully");
        }
    }

    useEffect( ()=>{
        getAuthState();
    },[])

    const value = {
        backendUrl,
        isLoggedin,setIsLoggedin,
        userData,setUserData,
        getUserData,
        logout,
        loading,
        apiUrl
    }

    return(
        <AppContent.Provider value={value}>
            {props.children}
        </AppContent.Provider>
    )
}